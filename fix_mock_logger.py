#!/usr/bin/env python3

"""
Script to fix mock_logger issues in test_event_dispatcher.py
"""

import re

def fix_mock_logger():
    file_path = "tests/unit/implementations/test_event_dispatcher.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find all functions that use mock_logger but don't create it
    functions = re.findall(r'def (test_[^(]+)\([^)]*\) -> bool:', content)
    
    for func_name in functions:
        # Skip functions that already create mock_logger
        if f"def {func_name}" in content and "mock_logger = Mock()" not in content:
            # Find the function body
            func_pattern = f"(def {func_name}.*?async def async_test.*?try:)(.*?from plugginger\\..*?\\))"
            match = re.search(func_pattern, content, re.DOTALL)
            if match and "mock_logger" in match.group(0):
                # Add mock_logger creation after imports
                before_imports = match.group(1)
                imports_section = match.group(2)
                
                # Check if this function uses mock_logger
                func_end_pattern = f"def {func_name}.*?(?=def |$)"
                func_content_match = re.search(func_end_pattern, content, re.DOTALL)
                if func_content_match and "mock_logger" in func_content_match.group(0):
                    # Insert mock_logger creation
                    replacement = before_imports + imports_section + "\n\n            # Create mock logger\n            mock_logger = Mock()"
                    content = content.replace(before_imports + imports_section, replacement)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("Fixed mock_logger issues")

if __name__ == "__main__":
    fix_mock_logger()
