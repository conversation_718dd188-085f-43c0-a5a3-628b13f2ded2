#!/usr/bin/env python3

"""
Simple script to add mock_logger = Mock() after imports in test functions
"""

def fix_mock_logger():
    file_path = "tests/unit/implementations/test_event_dispatcher.py"
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    new_lines = []
    i = 0
    while i < len(lines):
        line = lines[i]
        new_lines.append(line)
        
        # Check if this line ends an import block and the next lines use mock_logger
        if ("from plugginger.implementations.events import" in line and 
            i + 1 < len(lines) and lines[i + 1].strip() == ")"):
            # Add the closing parenthesis
            i += 1
            new_lines.append(lines[i])
            
            # Look ahead to see if mock_logger is used but not defined
            j = i + 1
            uses_mock_logger = False
            has_mock_logger_creation = False
            
            # Check the next 20 lines for mock_logger usage and creation
            for k in range(j, min(j + 20, len(lines))):
                if "mock_logger" in lines[k]:
                    uses_mock_logger = True
                if "mock_logger = Mock()" in lines[k]:
                    has_mock_logger_creation = True
                    break
                if lines[k].strip().startswith("def "):  # Next function
                    break
            
            # If uses mock_logger but doesn't create it, add creation
            if uses_mock_logger and not has_mock_logger_creation:
                new_lines.append("\n")
                new_lines.append("            # Create mock logger\n")
                new_lines.append("            mock_logger = Mock()\n")
        
        i += 1
    
    with open(file_path, 'w') as f:
        f.writelines(new_lines)
    
    print("Fixed mock_logger issues")

if __name__ == "__main__":
    fix_mock_logger()
