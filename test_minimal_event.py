#!/usr/bin/env python3

"""
Minimal test to verify event system functionality.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

async def main():
    try:
        print("Testing event system imports...")
        
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHand<PERSON>, SimpleEventDispatcher
        from unittest.mock import Mock
        
        print("✅ Imports successful")
        
        # Test basic functionality
        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
        
        print("✅ Components created")
        
        # Test event emission
        calls = []
        
        async def test_listener(event_data):
            calls.append(event_data)
        
        dispatcher.add_listener("test.event", test_listener)
        await dispatcher.emit_event("test.event", {"message": "hello"})
        await asyncio.sleep(0.1)
        
        print(f"✅ Event emitted, calls: {calls}")
        
        await dispatcher.shutdown()
        print("✅ Shutdown complete")
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
