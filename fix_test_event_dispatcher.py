#!/usr/bin/env python3

"""
Script to fix test_event_dispatcher.py by removing mock_logger parameters
and adding mock_logger creation inside each function.
"""

import re

def fix_test_file():
    file_path = "tests/unit/implementations/test_event_dispatcher.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # List of function names that need to be fixed
    functions_to_fix = [
        "test_event_dispatcher_max_concurrent_events_wait",
        "test_event_dispatcher_listener_params", 
        "test_event_dispatcher_listener_timeout_error",
        "test_event_dispatcher_listener_cancelled_error",
        "test_event_dispatcher_listener_exception_error",
        "test_event_dispatcher_emit_event_after_shutdown",
        "test_event_dispatcher_safe_call_listener_timeout_error",
        "test_event_dispatcher_safe_call_listener_cancelled_error",
        "test_event_dispatcher_safe_call_listener_exception_error"
    ]
    
    for func_name in functions_to_fix:
        # Remove mock_logger parameter from function signature
        pattern = f"def {func_name}\\(mock_logger: <PERSON><PERSON>\\) -> bool:"
        replacement = f"def {func_name}() -> bool:"
        content = re.sub(pattern, replacement, content)
        
        # Find the function and add mock_logger creation
        func_pattern = f"(def {func_name}\\(\\) -> bool:.*?async def async_test\\(\\) -> bool:.*?try:)"
        match = re.search(func_pattern, content, re.DOTALL)
        if match:
            # Find the imports section and add mock_logger creation after it
            func_content = match.group(1)
            
            # Look for the imports section
            imports_pattern = r"(from plugginger\.implementations\.events import.*?\))"
            imports_match = re.search(imports_pattern, func_content, re.DOTALL)
            if imports_match:
                imports_end = match.start(1) + imports_match.end(1)
                # Insert mock_logger creation after imports
                mock_logger_creation = "\n\n            # Create mock logger\n            mock_logger = Mock()"
                content = content[:imports_end] + mock_logger_creation + content[imports_end:]
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("Fixed test_event_dispatcher.py")

if __name__ == "__main__":
    fix_test_file()
