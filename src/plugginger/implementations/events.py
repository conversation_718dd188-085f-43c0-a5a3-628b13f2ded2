# src/plugginger/implementations/events.py

"""
Event system implementation using dependency injection.

This module provides concrete implementations of event-related interfaces
without circular import dependencies.
"""

from __future__ import annotations

import asyncio
import fnmatch
import inspect
from collections import defaultdict
from typing import Any

from plugginger.core.config import EventL<PERSON>enerFaultPolicy
from plugginger.core.types import EventHandlerType, EventType, LoggerCallable
from plugginger.interfaces.events import EventDispatcher, EventFaultHandler


class SimpleEventFaultHandler:
    """
    Simple implementation of EventFaultHandler interface.

    Manages error handling for event listeners based on a specified fault policy.
    """

    def __init__(
        self, policy: EventListenerFaultPolicy, logger: LoggerCallable | None = None
    ) -> None:
        """
        Initialize the fault handler.

        Args:
            policy: The fault policy to apply when errors occur
            logger: Optional logger function
        """
        self._policy = policy
        self._logger = logger or (lambda msg: None)
        self._dead_listeners: set[int] = set()

    def should_invoke(self, listener: EventHandlerType) -> bool:
        """
        Check if a listener should be invoked based on fault policy.

        Args:
            listener: The event listener to check

        Returns:
            True if the listener should be invoked, False if isolated
        """
        return id(listener) not in self._dead_listeners

    def handle_error(self, listener_qualname: str, listener_id: int, exception: Exception) -> None:
        """
        Handle an error from an event listener according to fault policy.

        Args:
            listener_qualname: Qualified name of the failing listener
            listener_id: Unique ID of the listener function
            exception: The exception that occurred

        Raises:
            EventListenerUnhandledError: If fault policy is FAIL_FAST
        """
        self._logger(f"[EventFaultHandler] Listener '{listener_qualname}' error: {exception!r}")

        if self._policy == EventListenerFaultPolicy.FAIL_FAST:
            from plugginger.core.exceptions import EventListenerUnhandledError

            if isinstance(exception, asyncio.TimeoutError):
                from plugginger.core.exceptions import EventListenerTimeoutError

                raise EventListenerTimeoutError(
                    f"Event listener '{listener_qualname}' timed out (FAIL_FAST policy active)."
                ) from exception
            else:
                raise EventListenerUnhandledError(
                    f"Unhandled exception in event listener '{listener_qualname}' (FAIL_FAST policy active)."
                ) from exception
        elif self._policy == EventListenerFaultPolicy.ISOLATE_AND_LOG:
            self._dead_listeners.add(listener_id)
            self._logger(
                f"[EventFaultHandler] Listener '{listener_qualname}' isolated due to error"
            )


class SimpleEventDispatcher:
    """
    Simple implementation of EventDispatcher interface.

    Manages event listener registrations and dispatches emitted events to
    all matching listeners, supporting wildcard patterns.
    """

    def __init__(
        self,
        fault_handler: EventFaultHandler,
        logger: LoggerCallable | None = None,
        default_listener_timeout: float = 5.0,
        max_concurrent_events: int = 100,
    ) -> None:
        """
        Initialize the event dispatcher.

        Args:
            fault_handler: Handler for listener faults
            logger: Optional logger function
            default_listener_timeout: Default timeout for listeners
            max_concurrent_events: Maximum concurrent event tasks
        """
        self._pattern_to_listeners: dict[str, list[EventHandlerType]] = defaultdict(list)
        self._fault_handler = fault_handler
        self._logger = logger or (lambda msg: None)
        self._default_listener_timeout = default_listener_timeout
        self._max_concurrent_events = max_concurrent_events
        self._active_tasks: set[asyncio.Task[None]] = set()
        self._shutdown_event = asyncio.Event()

    async def emit_event(self, event_type: EventType, event_data: dict[str, Any]) -> None:
        """
        Emit an event to all matching listeners.

        Args:
            event_type: The type of event to emit
            event_data: The data payload for the event
        """
        if self._shutdown_event.is_set():
            self._logger(f"[EventDispatcher] Ignoring event '{event_type}' - shutting down")
            return

        self._logger(f"[EventDispatcher] Emitting event: '{event_type}'")

        # Clean up completed tasks
        self._cleanup_completed_tasks()

        # Check concurrent event limit
        if len(self._active_tasks) >= self._max_concurrent_events:
            self._logger(
                f"[EventDispatcher] Warning: Max concurrent events reached ({self._max_concurrent_events})"
            )
            # Wait for some tasks to complete
            if self._active_tasks:
                try:
                    await asyncio.wait(
                        self._active_tasks, return_when=asyncio.FIRST_COMPLETED, timeout=0.1
                    )
                    self._cleanup_completed_tasks()
                except TimeoutError:
                    pass

        # Create tasks for matching listeners
        new_tasks: list[asyncio.Task[None]] = []

        for pattern, listeners in self._pattern_to_listeners.items():
            if fnmatch.fnmatchcase(event_type, pattern):
                self._logger(f"[EventDispatcher] Event '{event_type}' matched pattern '{pattern}'")

                for listener in listeners:
                    if not self._fault_handler.should_invoke(listener):
                        self._logger(
                            f"[EventDispatcher] Skipping isolated listener '{listener.__qualname__}'"
                        )
                        continue

                    try:
                        task = asyncio.create_task(
                            self._safe_call_listener(listener, event_data, event_type),
                            name=f"event_{event_type}_{listener.__qualname__}",
                        )
                        new_tasks.append(task)
                        self._active_tasks.add(task)
                    except Exception as e:
                        self._logger(
                            f"[EventDispatcher] Error creating task for listener '{listener.__qualname__}': {e}"
                        )

        if not new_tasks:
            self._logger(f"[EventDispatcher] No active listeners for event '{event_type}'")
        else:
            self._logger(
                f"[EventDispatcher] Created {len(new_tasks)} tasks for event '{event_type}'"
            )

    def add_listener(self, event_pattern: str, listener: EventHandlerType) -> None:
        """
        Register an event listener for a pattern.

        Args:
            event_pattern: Event pattern to listen for (supports wildcards)
            listener: The async handler function to register
        """
        self._pattern_to_listeners[event_pattern].append(listener)
        self._logger(
            f"[EventDispatcher] Registered listener '{listener.__qualname__}' for pattern '{event_pattern}'"
        )

    def remove_listener(self, event_pattern: str, listener: EventHandlerType) -> bool:
        """
        Remove an event listener.

        Args:
            event_pattern: Event pattern the listener was registered for
            listener: The handler function to remove

        Returns:
            True if the listener was found and removed, False otherwise
        """
        if event_pattern in self._pattern_to_listeners:
            try:
                self._pattern_to_listeners[event_pattern].remove(listener)
                self._logger(
                    f"[EventDispatcher] Removed listener '{listener.__qualname__}' for pattern '{event_pattern}'"
                )

                # Clean up empty pattern lists
                if not self._pattern_to_listeners[event_pattern]:
                    del self._pattern_to_listeners[event_pattern]

                return True
            except ValueError:
                pass

        return False

    def list_patterns(self) -> list[str]:
        """
        Get a list of all registered event patterns.

        Returns:
            List of event patterns that have listeners
        """
        return list(self._pattern_to_listeners.keys())

    async def shutdown(self) -> None:
        """
        Gracefully shutdown the event dispatcher.
        """
        self._logger("[EventDispatcher] Shutting down...")
        self._shutdown_event.set()

        # Cancel all active tasks
        for task in self._active_tasks:
            if not task.done():
                task.cancel()

        # Wait for all tasks to complete or be cancelled
        if self._active_tasks:
            await asyncio.gather(*self._active_tasks, return_exceptions=True)

        self._active_tasks.clear()
        self._logger("[EventDispatcher] Shutdown complete")

    def _cleanup_completed_tasks(self) -> None:
        """Remove completed tasks from the active tasks set."""
        completed_tasks = {task for task in self._active_tasks if task.done()}
        for task in completed_tasks:
            self._active_tasks.discard(task)
            # Log any exceptions
            try:
                if task.exception() is not None:
                    self._logger(
                        f"[EventDispatcher] Task completed with exception: {task.exception()}"
                    )
            except asyncio.CancelledError:
                pass

    async def _safe_call_listener(
        self, listener: EventHandlerType, event_data: dict[str, Any], event_type: EventType
    ) -> None:
        """
        Safely call an event listener with timeout and error handling.

        Args:
            listener: The event listener to call
            event_data: The event data payload
            event_type: The event type string
        """
        try:
            # Determine listener signature
            sig = inspect.signature(listener)
            params = list(sig.parameters.values())
            effective_params = [p for p in params if p.name not in ("self", "cls")]

            # Call listener with appropriate arguments
            if len(effective_params) == 2:
                # Listener expects (event_data, event_type)
                listener_coro = listener(event_data, event_type=event_type)
            else:
                # Listener expects (event_data)
                listener_coro = listener(event_data)

            # Call with timeout
            await asyncio.wait_for(listener_coro, timeout=self._default_listener_timeout)

        except TimeoutError as e:
            self._fault_handler.handle_error(listener.__qualname__, id(listener), e)
        except asyncio.CancelledError:
            # Task was cancelled - this is normal during shutdown
            raise
        except Exception as e:
            self._fault_handler.handle_error(listener.__qualname__, id(listener), e)
        finally:
            # Remove this task from active tasks
            current_task = asyncio.current_task()
            if current_task:
                self._active_tasks.discard(current_task)


class SimpleEventRegistry:
    """
    Simple implementation of EventRegistry interface.

    Manages event listener registration from plugins and tracks
    listeners for cleanup purposes.
    """

    def __init__(self, dispatcher: EventDispatcher, logger: LoggerCallable | None = None) -> None:
        """
        Initialize the event registry.

        Args:
            dispatcher: The event dispatcher to register listeners with
            logger: Optional logger function
        """
        self._dispatcher = dispatcher
        self._logger = logger or (lambda msg: None)
        self._plugin_listeners: dict[str, list[tuple[str, EventHandlerType]]] = {}

    def register_listener(
        self,
        plugin_instance_id: str,
        method_name: str,
        event_patterns: list[str],
        listener: EventHandlerType,
    ) -> None:
        """
        Register an event listener from a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance
            method_name: Name of the method on the plugin
            event_patterns: List of event patterns to listen for
            listener: The async handler function
        """
        # Register with dispatcher for each pattern
        for pattern in event_patterns:
            self._dispatcher.add_listener(pattern, listener)

        # Track for plugin cleanup
        if plugin_instance_id not in self._plugin_listeners:
            self._plugin_listeners[plugin_instance_id] = []

        for pattern in event_patterns:
            self._plugin_listeners[plugin_instance_id].append((pattern, listener))

        self._logger(
            f"[EventRegistry] Registered listener '{method_name}' for plugin '{plugin_instance_id}' "
            f"with {len(event_patterns)} patterns"
        )

    def unregister_plugin_listeners(self, plugin_instance_id: str) -> int:
        """
        Unregister all event listeners for a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            Number of listeners that were unregistered
        """
        if plugin_instance_id not in self._plugin_listeners:
            return 0

        listeners = self._plugin_listeners[plugin_instance_id]
        removed_count = 0

        # Remove from dispatcher
        for pattern, listener in listeners:
            if self._dispatcher.remove_listener(pattern, listener):
                removed_count += 1

        # Remove from tracking
        del self._plugin_listeners[plugin_instance_id]

        self._logger(
            f"[EventRegistry] Unregistered {removed_count} listeners for plugin '{plugin_instance_id}'"
        )
        return removed_count

    def get_plugin_listeners(self, plugin_instance_id: str) -> list[tuple[str, EventHandlerType]]:
        """
        Get all event listeners registered by a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of (pattern, handler) tuples for the plugin
        """
        return self._plugin_listeners.get(plugin_instance_id, []).copy()
