# src/plugginger/core/types.py

"""
Core type definitions with zero external dependencies.

This module contains fundamental type definitions that are used throughout
the Plugginger framework. These types have no dependencies on other parts
of the system and can be imported safely.
"""

from __future__ import annotations

from collections.abc import Awaitable, Callable, Sequence

# from concurrent.futures import Executor  # Unused import
from typing import Any, ParamSpec, TypeVar

# --- Generic Type Variables ---

# Generic type variable for return types
R = TypeVar("R")

# Parameter specification for generic callables
P = ParamSpec("P")

# --- Core Type Aliases ---

# Type alias for logger functions
LoggerCallable = Callable[[str], None]

# Type alias for service methods (async callables)
ServiceMethodType = Callable[P, Awaitable[R]]

# Type alias for event handlers (async callables that return None)
EventHandlerType = Callable[..., Awaitable[None]]

# Type alias for background task functions (sync callables)
BackgroundTaskType = Callable[P, R]

# Type alias for event pattern inputs
EventPatternInput = str | Sequence[str]

# --- Configuration Types ---

# Type for plugin configuration dictionaries
PluginConfigDict = dict[str, Any]

# Type for plugin configuration mapping (instance_id -> config)
PluginConfigMap = dict[str, PluginConfigDict]

# Type for executor configuration mapping (name -> config)
ExecutorConfigMap = dict[str, Any]

# --- Instance ID Types ---

# Type for plugin instance IDs
PluginInstanceId = str

# Type for service names (fully qualified)
ServiceName = str

# Type for event type strings
EventType = str

# --- Metadata Types ---

# Type for plugin metadata dictionaries
PluginMetadata = dict[str, Any]

# Type for service metadata dictionaries
ServiceMetadata = dict[str, Any]

# Type for event listener metadata dictionaries
EventListenerMetadata = dict[str, Any]

# --- Utility Types ---

# Type for optional values
Optional = TypeVar("Optional")

# Type for JSON-serializable values
JsonValue = str | int | float | bool | None | dict[str, Any] | list[Any]

# Type for configuration values
ConfigValue = JsonValue

# --- Forward Reference Strings ---
# These are used in type hints to avoid circular imports

# Forward reference for PluginBase
PLUGIN_BASE_TYPE = "PluginBase"

# Forward reference for PluggingerAppInstance
APP_INSTANCE_TYPE = "PluggingerAppInstance"

# Forward reference for PluggingerAppBuilder
APP_BUILDER_TYPE = "PluggingerAppBuilder"

# Forward reference for DIContainer
DI_CONTAINER_TYPE = "DIContainer"
