# src/plugginger/api/builder.py

"""
Application builder API using dependency injection.

This module provides the PluggingerAppBuilder class for constructing and
configuring Plugginger applications with a fluent interface.
"""

from __future__ import annotations

from typing import TypeVar, cast

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.plugin import PluginBase, get_plugin_metadata, is_plugin_class
from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import (
    ConfigurationError,
    PluginRegistrationError,
)
from plugginger.core.types import PluginInstanceId
from plugginger.implementations.container import DIContainer
from plugginger.implementations.events import Simple<PERSON>ventDispatcher, SimpleEventFaultHandler
from plugginger.implementations.services import SimpleServiceDispatcher

T = TypeVar("T", bound=PluginBase)


class PluggingerAppBuilder:
    """
    Builder for constructing Plugginger applications.

    This class provides a fluent interface for registering plugins, configuring
    services and events, and building the final application instance.

    Example:
        ```python
        # Create and configure the application
        app = (PluggingerAppBuilder()
            .with_name("MyApp")
            .register_plugin(DatabasePlugin)
            .register_plugin(UserServicePlugin)
            .register_plugin(WebAPIPlugin)
            .build())

        # Use the application
        result = await app.call_service("user.get_by_id", user_id=123)
        await app.emit_event("user.accessed", {"user_id": 123})
        ```
    """

    def __init__(self) -> None:
        """Initialize the application builder."""
        self._app_name = DEFAULT_APP_NAME
        self._plugin_classes: list[type[PluginBase]] = []
        self._plugin_instances: dict[str, PluginBase] = {}
        self._container = DIContainer()
        self._service_dispatcher = SimpleServiceDispatcher()

        # Create default fault handler with ISOLATE policy
        from plugginger.core.constants import EventFaultPolicy

        fault_handler = SimpleEventFaultHandler(policy=EventFaultPolicy.ISOLATE)
        self._event_dispatcher = SimpleEventDispatcher(fault_handler=fault_handler)
        self._is_built = False

    def with_name(self, name: str) -> PluggingerAppBuilder:
        """
        Set the application name.

        Args:
            name: Name for the application

        Returns:
            Self for method chaining

        Raises:
            ConfigurationError: If name is invalid
        """
        if self._is_built:
            raise ConfigurationError("Cannot modify builder after build() has been called")

        if not name or not isinstance(name, str):
            raise ConfigurationError(f"App name must be a non-empty string, got: {name!r}")

        self._app_name = name
        return self

    def register_plugin(self, plugin_class: type[T]) -> PluggingerAppBuilder:
        """
        Register a plugin class with the application.

        Args:
            plugin_class: Plugin class to register

        Returns:
            Self for method chaining

        Raises:
            PluginRegistrationError: If plugin registration fails
        """
        if self._is_built:
            raise ConfigurationError("Cannot modify builder after build() has been called")

        # Validate plugin class
        if not is_plugin_class(plugin_class):
            raise PluginRegistrationError(
                f"Class '{plugin_class.__name__}' is not a valid plugin class. "
                f"Make sure it inherits from PluginBase and has the @plugin decorator."
            )

        # Check for duplicate registration
        if plugin_class in self._plugin_classes:
            raise PluginRegistrationError(
                f"Plugin class '{plugin_class.__name__}' is already registered"
            )

        self._plugin_classes.append(plugin_class)
        return self

    def register_plugins(self, *plugin_classes: type[PluginBase]) -> PluggingerAppBuilder:
        """
        Register multiple plugin classes with the application.

        Args:
            *plugin_classes: Plugin classes to register

        Returns:
            Self for method chaining
        """
        for plugin_class in plugin_classes:
            self.register_plugin(plugin_class)
        return self

    def build(self) -> PluggingerAppInstance:
        """
        Build the application instance.

        This method instantiates all registered plugins, sets up dependency
        injection, registers services and events, and creates the final
        application instance.

        Returns:
            Configured PluggingerAppInstance

        Raises:
            ConfigurationError: If build fails due to configuration issues
            PluginRegistrationError: If plugin setup fails
        """
        if self._is_built:
            raise ConfigurationError("build() can only be called once per builder instance")

        try:
            # Mark as built to prevent further modifications
            self._is_built = True

            # Setup dependency injection container
            self._setup_container()

            # Instantiate plugins
            self._instantiate_plugins()

            # Register services and events
            self._register_services_and_events()

            # Create and return app instance
            return self._create_app_instance()

        except Exception as e:
            # Reset built state on failure
            self._is_built = False
            if isinstance(e, ConfigurationError | PluginRegistrationError):
                raise
            raise ConfigurationError(f"Failed to build application: {e}") from e

    def _setup_container(self) -> None:
        """Setup the dependency injection container."""
        # Register core services in the container
        self._container.register(SimpleServiceDispatcher, self._service_dispatcher)
        self._container.register(SimpleEventDispatcher, self._event_dispatcher)

    def _instantiate_plugins(self) -> None:
        """Instantiate all registered plugin classes."""
        for plugin_class in self._plugin_classes:
            try:
                # Get plugin metadata
                metadata = get_plugin_metadata(plugin_class)
                plugin_name = metadata["name"]

                # Generate unique instance ID
                instance_id = self._generate_instance_id(plugin_name)

                # Create plugin instance with dependency injection
                plugin_instance = plugin_class()
                plugin_instance._set_plugin_instance_id(instance_id)

                # Store the instance
                self._plugin_instances[instance_id] = plugin_instance

            except Exception as e:
                raise PluginRegistrationError(
                    f"Failed to instantiate plugin '{plugin_class.__name__}': {e}"
                ) from e

    def _register_services_and_events(self) -> None:
        """Register services and events from all plugin instances."""
        for instance_id, plugin_instance in self._plugin_instances.items():
            try:
                # Register services
                self._register_plugin_services(instance_id, plugin_instance)

                # Register event listeners
                self._register_plugin_events(instance_id, plugin_instance)

            except Exception as e:
                raise PluginRegistrationError(
                    f"Failed to register services/events for plugin '{instance_id}': {e}"
                ) from e

    def _register_plugin_services(self, instance_id: str, plugin_instance: PluginBase) -> None:
        """Register services from a plugin instance."""
        from plugginger.api.service import extract_service_methods

        services = extract_service_methods(plugin_instance)
        for service_name, service_method in services.items():
            self._service_dispatcher.add_service(service_name, service_method)

    def _register_plugin_events(self, instance_id: str, plugin_instance: PluginBase) -> None:
        """Register event listeners from a plugin instance."""
        from plugginger.api.events import get_listener_patterns

        patterns = get_listener_patterns(plugin_instance)
        for pattern, handler, _metadata in patterns:
            self._event_dispatcher.add_listener(pattern, handler)

    def _create_app_instance(self) -> PluggingerAppInstance:
        """Create the final application instance."""
        # Register the app instance in the container for DI
        app_instance = PluggingerAppInstance(
            service_dispatcher=self._service_dispatcher,
            event_dispatcher=self._event_dispatcher,
            app_name=self._app_name,
        )

        # Register app instance in container for get_app_instance() function
        self._container.register(PluggingerAppInstance, app_instance)

        return app_instance

    def _generate_instance_id(self, plugin_name: str) -> PluginInstanceId:
        """
        Generate a unique instance ID for a plugin.

        Args:
            plugin_name: Name of the plugin

        Returns:
            Unique instance ID
        """
        # Simple implementation - could be enhanced with UUIDs or counters
        existing_count = sum(
            1 for id in self._plugin_instances.keys() if id.startswith(plugin_name)
        )
        if existing_count == 0:
            return cast(PluginInstanceId, plugin_name)
        else:
            return cast(PluginInstanceId, f"{plugin_name}_{existing_count + 1}")

    @property
    def app_name(self) -> str:
        """Get the configured application name."""
        return self._app_name

    @property
    def plugin_count(self) -> int:
        """Get the number of registered plugin classes."""
        return len(self._plugin_classes)

    @property
    def is_built(self) -> bool:
        """Check if the application has been built."""
        return self._is_built

    def __repr__(self) -> str:
        """String representation for debugging."""
        return (
            f"PluggingerAppBuilder("
            f"name='{self._app_name}', "
            f"plugins={len(self._plugin_classes)}, "
            f"built={self._is_built})"
        )


# Convenience function for quick app creation


def create_app(
    name: str = DEFAULT_APP_NAME, *plugin_classes: type[PluginBase]
) -> PluggingerAppInstance:
    """
    Convenience function to quickly create an application.

    Args:
        name: Application name
        *plugin_classes: Plugin classes to register

    Returns:
        Configured PluggingerAppInstance

    Example:
        ```python
        app = create_app("MyApp", DatabasePlugin, UserServicePlugin)
        ```
    """
    builder = PluggingerAppBuilder().with_name(name)

    for plugin_class in plugin_classes:
        builder.register_plugin(plugin_class)

    return builder.build()
