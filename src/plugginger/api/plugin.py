# src/plugginger/api/plugin.py

"""
Plugin base class and @plugin decorator using dependency injection.

This module provides the core plugin functionality without circular imports
by using the DI container for all dependencies.
"""

from __future__ import annotations

import inspect
from typing import Any, TypeVar, cast

from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.core.exceptions import PluginRegistrationError
from plugginger.core.types import PluginInstanceId

T = TypeVar("T")


class PluginBase:
    """
    Base class for all plugins in the Plugginger framework.

    This class provides the foundation for plugin development with automatic
    dependency injection and lifecycle management.
    """

    def __init__(self, **injected_dependencies: Any) -> None:
        """
        Initialize the plugin with injected dependencies.

        Args:
            **injected_dependencies: Dependencies injected by the DI container
        """
        # Store injected dependencies
        for name, dependency in injected_dependencies.items():
            setattr(self, name, dependency)

        # Plugin metadata
        self._plugin_instance_id: PluginInstanceId | None = None
        self._is_setup: bool = False
        self._is_torn_down: bool = False

    async def setup(self) -> None:
        """
        Setup hook called when the plugin is initialized.

        Override this method to perform plugin-specific initialization.
        This method is called after dependency injection but before
        the plugin is registered with services and events.
        """
        pass

    async def teardown(self) -> None:
        """
        Teardown hook called when the plugin is being shut down.

        Override this method to perform plugin-specific cleanup.
        This method is called before services and events are unregistered.
        """
        pass

    @property
    def plugin_instance_id(self) -> PluginInstanceId:
        """
        Get the unique instance ID for this plugin.

        Returns:
            The plugin instance ID

        Raises:
            RuntimeError: If the plugin has not been registered yet
        """
        if self._plugin_instance_id is None:
            raise RuntimeError("Plugin has not been registered yet")
        return self._plugin_instance_id

    @property
    def is_setup(self) -> bool:
        """Check if the plugin has been set up."""
        return self._is_setup

    @property
    def is_torn_down(self) -> bool:
        """Check if the plugin has been torn down."""
        return self._is_torn_down

    def _set_plugin_instance_id(self, instance_id: PluginInstanceId) -> None:
        """
        Set the plugin instance ID (internal use only).

        Args:
            instance_id: The unique instance ID for this plugin
        """
        self._plugin_instance_id = instance_id

    def _mark_setup(self) -> None:
        """Mark the plugin as set up (internal use only)."""
        self._is_setup = True

    def _mark_torn_down(self) -> None:
        """Mark the plugin as torn down (internal use only)."""
        self._is_torn_down = True


def plugin(
    name: str | None = None,
    version: str = "1.0.0",
    dependencies: list[str] | None = None,
    optional_dependencies: list[str] | None = None,
) -> Any:
    """
    Decorator to mark a class as a Plugginger plugin.

    This decorator adds metadata to the class that the framework uses
    for plugin registration and dependency management.

    Args:
        name: Optional custom name for the plugin (defaults to class name)
        version: Version string for the plugin (default: "1.0.0")
        dependencies: List of required plugin dependencies
        optional_dependencies: List of optional plugin dependencies

    Returns:
        The decorated class with plugin metadata

    Example:
        ```python
        @plugin(name="database", version="2.1.0", dependencies=["config"])
        class DatabasePlugin(PluginBase):
            async def setup(self):
                # Plugin initialization
                pass
        ```
    """

    def decorator(cls: type[T]) -> type[T]:
        # Validate that the class inherits from PluginBase
        if not issubclass(cls, PluginBase):
            raise PluginRegistrationError(
                f"Plugin class '{cls.__name__}' must inherit from PluginBase"
            )

        # Validate that the class is not abstract
        if inspect.isabstract(cls):
            raise PluginRegistrationError(f"Plugin class '{cls.__name__}' cannot be abstract")

        # Determine plugin name
        plugin_name = name or cls.__name__

        # Validate plugin name
        if not plugin_name or not isinstance(plugin_name, str):
            raise PluginRegistrationError(
                f"Plugin name must be a non-empty string, got: {plugin_name!r}"
            )

        # Create plugin metadata
        metadata = {
            "name": plugin_name,
            "version": version,
            "dependencies": dependencies or [],
            "optional_dependencies": optional_dependencies or [],
            "class_name": cls.__name__,
            "module": cls.__module__,
        }

        # Attach metadata to the class
        setattr(cls, PLUGIN_METADATA_KEY, metadata)

        return cast(type[T], cls)

    return decorator


def get_plugin_metadata(plugin_class: type) -> dict[str, Any]:
    """
    Get plugin metadata from a plugin class.

    Args:
        plugin_class: The plugin class to inspect

    Returns:
        Dictionary containing plugin metadata

    Raises:
        PluginRegistrationError: If the class is not a valid plugin
    """
    if not hasattr(plugin_class, PLUGIN_METADATA_KEY):
        raise PluginRegistrationError(
            f"Class '{plugin_class.__name__}' is not a valid plugin (missing @plugin decorator)"
        )

    return cast(dict[str, Any], getattr(plugin_class, PLUGIN_METADATA_KEY))


def is_plugin_class(cls: type) -> bool:
    """
    Check if a class is a valid plugin class.

    Args:
        cls: The class to check

    Returns:
        True if the class is a valid plugin, False otherwise
    """
    return (
        hasattr(cls, PLUGIN_METADATA_KEY)
        and issubclass(cls, PluginBase)
        and not inspect.isabstract(cls)
    )
