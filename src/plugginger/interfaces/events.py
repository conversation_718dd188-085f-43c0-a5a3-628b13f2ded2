# src/plugginger/interfaces/events.py

"""
Event system interface definitions.

This module defines the abstract interfaces for event management and dispatch
using Python's Protocol system. These interfaces enable dependency injection
and testing without circular imports.
"""

from __future__ import annotations

from typing import Any, Protocol

from plugginger.core.types import EventHandlerType, EventType


class EventDispatcher(Protocol):
    """Interface for event emission and dispatch."""

    async def emit_event(self, event_type: EventType, event_data: dict[str, Any]) -> None:
        """
        Emit an event to all matching listeners.

        Args:
            event_type: The type of event to emit
            event_data: The data payload for the event
        """
        ...

    def add_listener(self, event_pattern: str, listener: EventHandlerType) -> None:
        """
        Register an event listener for a pattern.

        Args:
            event_pattern: Event pattern to listen for (supports wildcards)
            listener: The async handler function to register
        """
        ...

    def remove_listener(self, event_pattern: str, listener: EventHandlerType) -> bool:
        """
        Remove an event listener.

        Args:
            event_pattern: Event pattern the listener was registered for
            listener: The handler function to remove

        Returns:
            True if the listener was found and removed, False otherwise
        """
        ...

    def list_patterns(self) -> list[str]:
        """
        Get a list of all registered event patterns.

        Returns:
            List of event patterns that have listeners
        """
        ...

    async def shutdown(self) -> None:
        """
        Gracefully shutdown the event dispatcher.

        This should cancel all active event processing tasks and clean up resources.
        """
        ...


class EventRegistry(Protocol):
    """Interface for event listener registration and discovery."""

    def register_listener(
        self,
        plugin_instance_id: str,
        method_name: str,
        event_patterns: list[str],
        listener: EventHandlerType,
    ) -> None:
        """
        Register an event listener from a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance
            method_name: Name of the method on the plugin
            event_patterns: List of event patterns to listen for
            listener: The async handler function
        """
        ...

    def unregister_plugin_listeners(self, plugin_instance_id: str) -> int:
        """
        Unregister all event listeners for a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            Number of listeners that were unregistered
        """
        ...

    def get_plugin_listeners(self, plugin_instance_id: str) -> list[tuple[str, EventHandlerType]]:
        """
        Get all event listeners registered by a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of (pattern, handler) tuples for the plugin
        """
        ...


class EventBridge(Protocol):
    """Interface for event bridging between app layers (fractal composition)."""

    def setup_bridge(
        self,
        direction: str,  # "inbound" or "outbound"
        pattern: str,
        source_dispatcher: EventDispatcher,
        target_dispatcher: EventDispatcher,
    ) -> None:
        """
        Setup an event bridge between two dispatchers.

        Args:
            direction: Direction of the bridge ("inbound" or "outbound")
            pattern: Event pattern to bridge
            source_dispatcher: Source event dispatcher
            target_dispatcher: Target event dispatcher
        """
        ...

    async def teardown_bridges(self) -> None:
        """
        Teardown all event bridges.
        """
        ...


class EventValidator(Protocol):
    """Interface for event payload validation."""

    def validate_payload(
        self, event_type: EventType, event_data: dict[str, Any], expected_schema: type | None = None
    ) -> dict[str, Any]:
        """
        Validate and potentially transform event payload.

        Args:
            event_type: The type of event
            event_data: The raw event data
            expected_schema: Optional Pydantic model for validation

        Returns:
            Validated/transformed event data

        Raises:
            EventPayloadValidationError: If validation fails
        """
        ...


class EventFaultHandler(Protocol):
    """Interface for handling event listener faults."""

    def should_invoke(self, listener: EventHandlerType) -> bool:
        """
        Check if a listener should be invoked based on fault policy.

        Args:
            listener: The event listener to check

        Returns:
            True if the listener should be invoked, False if isolated
        """
        ...

    def handle_error(self, listener_qualname: str, listener_id: int, exception: Exception) -> None:
        """
        Handle an error from an event listener according to fault policy.

        Args:
            listener_qualname: Qualified name of the failing listener
            listener_id: Unique ID of the listener function
            exception: The exception that occurred

        Raises:
            EventListenerUnhandledError: If fault policy is FAIL_FAST
        """
        ...
