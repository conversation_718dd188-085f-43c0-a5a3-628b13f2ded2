# tests/unit/api/test_event_api_clean.py

"""
Clean pytest-compatible tests for the Event API.
"""

from __future__ import annotations

import os
import sys
from typing import Any

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_on_event_decorator() -> None:
    """Test @on_event decorator functionality."""
    from plugginger.api.events import (
        get_event_listener_metadata,
        is_event_listener_method,
        on_event,
    )
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="event_test_plugin")
    class EventTestPlugin(PluginBase):
        @on_event(
            "user.created", timeout_seconds=10.0, description="Handle user creation", priority=5
        )
        async def on_user_created(self, event_data: dict[str, Any]) -> None:
            pass

        @on_event(["user.*", "admin.user.*"], priority=10)
        async def on_any_user_event(self, event_data: dict[str, Any], event_type: str) -> None:
            pass

        @on_event("system.shutdown")
        async def on_system_shutdown(self, event_data: dict[str, Any]) -> None:
            pass

    # Test single pattern metadata
    metadata = get_event_listener_metadata(EventTestPlugin.on_user_created)
    assert metadata["patterns"] == ["user.created"]
    assert metadata["timeout_seconds"] == 10.0
    assert metadata["description"] == "Handle user creation"
    assert metadata["priority"] == 5
    assert metadata["method_name"] == "on_user_created"
    assert len(metadata["parameters"]) == 1  # event_data

    # Test multiple patterns metadata
    metadata = get_event_listener_metadata(EventTestPlugin.on_any_user_event)
    assert metadata["patterns"] == ["user.*", "admin.user.*"]
    assert metadata["priority"] == 10
    assert len(metadata["parameters"]) == 2  # event_data, event_type

    # Test default values
    metadata = get_event_listener_metadata(EventTestPlugin.on_system_shutdown)
    assert metadata["patterns"] == ["system.shutdown"]
    assert metadata["timeout_seconds"] is None
    assert metadata["description"] is None
    assert metadata["priority"] == 0

    # Test is_event_listener_method
    assert is_event_listener_method(EventTestPlugin.on_user_created)
    assert is_event_listener_method(EventTestPlugin.on_any_user_event)
    assert is_event_listener_method(EventTestPlugin.on_system_shutdown)


def test_event_validation_non_async() -> None:
    """Test event listener validation for non-async methods."""
    from plugginger.api.events import on_event
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import EventDefinitionError

    with pytest.raises(EventDefinitionError, match="must be async"):
        @plugin(name="invalid_event_plugin")
        class InvalidEventPlugin(PluginBase):
            @on_event("test.event")
            def sync_listener(self, event_data: dict[str, Any]) -> None:  # Not async!
                pass


def test_event_validation_no_self() -> None:
    """Test event listener validation for methods without self."""
    from plugginger.api.events import on_event
    from plugginger.core.exceptions import EventDefinitionError

    with pytest.raises(EventDefinitionError, match="must be an instance method"):
        @on_event("test.event")
        async def standalone_listener(event_data: dict[str, Any]) -> None:
            pass


def test_event_validation_invalid_timeout() -> None:
    """Test event listener validation for invalid timeout."""
    from plugginger.api.events import on_event
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import EventDefinitionError

    with pytest.raises(EventDefinitionError, match="positive number"):
        @plugin(name="timeout_plugin")
        class TimeoutPlugin(PluginBase):
            @on_event("test.event", timeout_seconds=-1)
            async def invalid_timeout_listener(self, event_data: dict[str, Any]) -> None:
                pass


def test_event_validation_invalid_priority() -> None:
    """Test event listener validation for invalid priority."""
    from plugginger.api.events import on_event
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import EventDefinitionError

    with pytest.raises(EventDefinitionError, match="must be an integer"):
        @plugin(name="priority_plugin")
        class PriorityPlugin(PluginBase):
            @on_event("test.event", priority="high")  # type: ignore
            async def invalid_priority_listener(self, event_data: dict[str, Any]) -> None:
                pass


def test_event_validation_non_listener() -> None:
    """Test getting metadata from non-listener method."""
    from plugginger.api.events import get_event_listener_metadata
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import EventDefinitionError

    @plugin(name="normal_plugin")
    class NormalPlugin(PluginBase):
        async def normal_method(self, data: dict[str, Any]) -> None:
            pass

    with pytest.raises(EventDefinitionError, match="not a valid event listener"):
        get_event_listener_metadata(NormalPlugin.normal_method)


def test_extract_event_listeners() -> None:
    """Test extracting event listeners from plugin instances."""
    import inspect

    from plugginger.api.events import extract_event_listeners, get_listener_patterns, on_event
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="multi_listener_plugin")
    class MultiListenerPlugin(PluginBase):
        @on_event("event.one", priority=10)
        async def listener_one(self, event_data: dict[str, Any]) -> None:
            pass

        @on_event(["event.two", "event.three"], priority=5)
        async def listener_two(self, event_data: dict[str, Any], event_type: str) -> None:
            pass

        async def non_listener_method(self, data: dict[str, Any]) -> None:
            pass

        def _private_method(self) -> None:
            pass

    # Create plugin instance
    plugin_instance = MultiListenerPlugin()

    # Extract listeners
    listeners = extract_event_listeners(plugin_instance)

    # Verify extracted listeners
    assert len(listeners) == 2
    assert "listener_one" in listeners
    assert "listener_two" in listeners
    assert "non_listener_method" not in listeners
    assert "_private_method" not in listeners

    # Verify listeners are bound methods
    assert inspect.ismethod(listeners["listener_one"])
    assert inspect.ismethod(listeners["listener_two"])

    # Test get_listener_patterns
    patterns = get_listener_patterns(plugin_instance)

    # Should have 3 patterns total (1 + 2)
    assert len(patterns) == 3

    # Check patterns are sorted by priority (higher first)
    assert patterns[0][0] == "event.one"  # Priority 10
    assert patterns[0][2]["priority"] == 10

    assert patterns[1][0] in ["event.two", "event.three"]  # Priority 5
    assert patterns[1][2]["priority"] == 5

    assert patterns[2][0] in ["event.two", "event.three"]  # Priority 5
    assert patterns[2][2]["priority"] == 5


def test_event_listener_signature_validation() -> None:
    """Test event listener signature validation."""
    from plugginger.api.events import on_event, validate_event_listener_signature
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import EventDefinitionError

    @plugin(name="signature_plugin")
    class SignaturePlugin(PluginBase):
        @on_event("valid.event")
        async def valid_listener_one_param(self, event_data: dict[str, Any]) -> None:
            pass

        @on_event("valid.event")
        async def valid_listener_two_params(self, event_data: dict[str, Any], event_type: str) -> None:
            pass

        @on_event("invalid.event")
        async def invalid_too_many_params(
            self, event_data: dict[str, Any], event_type: str, extra: str
        ) -> None:
            pass

        @on_event("invalid.event")
        async def invalid_varargs(self, event_data: dict[str, Any], *args: Any) -> None:
            pass

        @on_event("invalid.event")
        async def invalid_kwargs(self, event_data: dict[str, Any], **kwargs: Any) -> None:
            pass

    # Test valid signatures
    validate_event_listener_signature(SignaturePlugin.valid_listener_one_param)
    validate_event_listener_signature(SignaturePlugin.valid_listener_two_params)

    # Test invalid signatures
    with pytest.raises(EventDefinitionError, match="must have 1 or 2 parameters"):
        validate_event_listener_signature(SignaturePlugin.invalid_too_many_params)

    with pytest.raises(EventDefinitionError, match=r"\*args"):
        validate_event_listener_signature(SignaturePlugin.invalid_varargs)

    with pytest.raises(EventDefinitionError, match=r"\*\*kwargs"):
        validate_event_listener_signature(SignaturePlugin.invalid_kwargs)


@pytest.mark.asyncio
async def test_event_listener_execution() -> None:
    """Test actual event listener method execution."""
    from plugginger.api.events import extract_event_listeners, on_event
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="execution_plugin")
    class ExecutionPlugin(PluginBase):
        def __init__(self, **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)
            self.events_received: list[tuple[str, dict[str, Any]]] = []

        @on_event("user.created")
        async def on_user_created(self, event_data: dict[str, Any]) -> None:
            self.events_received.append(("user.created", event_data))

        @on_event(["system.*"])
        async def on_system_event(self, event_data: dict[str, Any], event_type: str) -> None:
            self.events_received.append((event_type, event_data))

    # Create plugin instance
    plugin_instance = ExecutionPlugin()

    # Extract and call listeners
    listeners = extract_event_listeners(plugin_instance)

    # Test single parameter listener
    await listeners["on_user_created"]({"user_id": 123, "name": "John"})
    assert len(plugin_instance.events_received) == 1
    assert plugin_instance.events_received[0] == (
        "user.created",
        {"user_id": 123, "name": "John"},
    )

    # Test two parameter listener
    await listeners["on_system_event"]({"action": "startup"}, "system.startup")
    assert len(plugin_instance.events_received) == 2
    assert plugin_instance.events_received[1] == ("system.startup", {"action": "startup"})
