"""Test background task decorator."""

import asyncio

import pytest

from plugginger.api.background import (
    background_task,
    get_background_task_executor,
    is_background_task,
)
from plugginger.core.exceptions import BackgroundTaskError


def test_background_task_direct_decoration():
    """Test direct decoration with @background_task."""

    @background_task
    def sync_function(x: int, y: int) -> int:
        return x + y

    # Should be marked as background task
    assert is_background_task(sync_function)
    assert get_background_task_executor(sync_function) == "default"

    # Should be async now
    assert asyncio.iscoroutinefunction(sync_function)


def test_background_task_factory_decoration():
    """Test factory decoration with @background_task(executor="custom")."""

    @background_task(executor="custom_pool")
    def sync_function(x: int) -> int:
        return x * 2

    # Should be marked as background task with custom executor
    assert is_background_task(sync_function)
    assert get_background_task_executor(sync_function) == "custom_pool"

    # Should be async now
    assert asyncio.iscoroutinefunction(sync_function)


@pytest.mark.asyncio
async def test_background_task_execution():
    """Test that background tasks execute correctly."""

    @background_task
    def compute_sum(numbers: list[int]) -> int:
        return sum(numbers)

    # Execute the background task
    result = await compute_sum([1, 2, 3, 4, 5])
    assert result == 15


def test_background_task_rejects_async_functions():
    """Test that @background_task rejects async functions."""

    with pytest.raises(TypeError, match="can only be applied to synchronous functions"):
        @background_task
        async def async_function() -> None:
            pass


@pytest.mark.asyncio
async def test_background_task_error_handling():
    """Test error handling in background tasks."""

    @background_task
    def failing_function() -> None:
        raise ValueError("Test error")

    with pytest.raises(BackgroundTaskError, match="Background task 'failing_function' failed"):
        await failing_function()


def test_background_task_metadata():
    """Test that background task metadata is preserved."""

    def original_function(x: int) -> str:
        """Original docstring."""
        return str(x)

    decorated = background_task(original_function)

    # Metadata should be preserved
    assert decorated.__name__ == "original_function"
    assert "Original docstring" in decorated.__doc__

    # Background task metadata should be present
    assert hasattr(decorated, "_background_task_executor")
    assert hasattr(decorated, "_background_task_original")
    assert hasattr(decorated, "_is_background_task")


def test_is_background_task_with_regular_function():
    """Test is_background_task with regular functions."""

    def regular_function() -> None:
        pass

    assert not is_background_task(regular_function)


def test_get_background_task_executor_with_regular_function():
    """Test get_background_task_executor with regular functions."""

    def regular_function() -> None:
        pass

    # Should return default for non-background tasks
    assert get_background_task_executor(regular_function) == "default"
