# tests/unit/api/test_service_clean.py

"""
Clean pytest-compatible tests for the Service API.
"""

from __future__ import annotations

import inspect
import os
import sys
from typing import Any

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_service_decorator() -> None:
    """Test @service decorator functionality."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import get_service_metadata, is_service_method, service

    @plugin(name="test_plugin")
    class TestPlugin(PluginBase):
        @service(name="custom_service", timeout_seconds=10.0, description="Test service")
        async def test_method(self, param1: str, param2: int = 42) -> str:
            return f"{param1}_{param2}"

        @service()  # Default name
        async def default_name_service(self, value: float) -> float:
            return value * 2

    # Test custom service metadata
    metadata = get_service_metadata(TestPlugin.test_method)
    assert metadata["name"] == "custom_service"
    assert metadata["timeout_seconds"] == 10.0
    assert metadata["description"] == "Test service"
    assert metadata["method_name"] == "test_method"
    assert len(metadata["parameters"]) == 2

    # Check parameter metadata
    param1 = metadata["parameters"][0]
    assert param1["name"] == "param1"
    assert param1["annotation"] == str or param1["annotation"] == "str"
    assert param1["default"] is None

    param2 = metadata["parameters"][1]
    assert param2["name"] == "param2"
    assert param2["annotation"] == int or param2["annotation"] == "int"
    assert param2["default"] == 42

    # Test default name service
    metadata = get_service_metadata(TestPlugin.default_name_service)
    assert metadata["name"] == "default_name_service"
    assert metadata["timeout_seconds"] is None
    assert metadata["description"] is None

    # Test is_service_method
    assert is_service_method(TestPlugin.test_method)
    assert is_service_method(TestPlugin.default_name_service)


def test_service_validation_non_async() -> None:
    """Test service validation for non-async methods."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import service
    from plugginger.core.exceptions import ServiceDefinitionError

    with pytest.raises(ServiceDefinitionError, match="must be async"):

        @plugin(name="invalid_plugin")
        class InvalidPlugin(PluginBase):
            @service()
            def sync_method(self) -> str:  # Not async!
                return "sync"


def test_service_validation_no_self() -> None:
    """Test service validation for methods without self."""
    from plugginger.api.service import service
    from plugginger.core.exceptions import ServiceDefinitionError

    with pytest.raises(ServiceDefinitionError, match="must be an instance method"):

        @service()
        async def standalone_function() -> None:
            pass


def test_service_validation_invalid_timeout() -> None:
    """Test service validation for invalid timeout."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import service
    from plugginger.core.exceptions import ServiceDefinitionError

    with pytest.raises(ServiceDefinitionError, match="positive number"):

        @plugin(name="timeout_plugin")
        class TimeoutPlugin(PluginBase):
            @service(timeout_seconds=-1)
            async def invalid_timeout(self) -> None:
                pass


def test_service_validation_non_service() -> None:
    """Test getting metadata from non-service method."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import get_service_metadata
    from plugginger.core.exceptions import ServiceDefinitionError

    @plugin(name="normal_plugin")
    class NormalPlugin(PluginBase):
        async def normal_method(self) -> None:
            pass

    with pytest.raises(ServiceDefinitionError, match="not a valid service"):
        get_service_metadata(NormalPlugin.normal_method)


def test_extract_service_methods() -> None:
    """Test extracting service methods from plugin instances."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import extract_service_methods, service

    @plugin(name="multi_service_plugin")
    class MultiServicePlugin(PluginBase):
        @service(name="service_one")
        async def method_one(self, x: int) -> int:
            return x * 2

        @service(name="service_two")
        async def method_two(self, y: str) -> str:
            return y.upper()

        async def non_service_method(self, z: float) -> float:
            return z + 1.0

        def _private_method(self) -> None:
            pass

    # Create plugin instance
    plugin_instance = MultiServicePlugin()

    # Extract services
    services = extract_service_methods(plugin_instance)

    # Verify extracted services
    assert len(services) == 2
    assert "service_one" in services
    assert "service_two" in services
    assert "non_service_method" not in services
    assert "_private_method" not in services

    # Verify services are bound methods
    assert inspect.ismethod(services["service_one"])
    assert inspect.ismethod(services["service_two"])


def test_service_method_validation() -> None:
    """Test service method signature validation."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import (
        get_service_call_signature,
        service,
        validate_service_method_signature,
    )
    from plugginger.core.exceptions import ServiceDefinitionError

    @plugin(name="validation_plugin")
    class ValidationPlugin(PluginBase):
        @service()
        async def valid_service(self, param1: str, param2: int = 10) -> str:
            return f"{param1}_{param2}"

        @service()
        async def varargs_service(self, *args: Any) -> None:  # Invalid: *args
            pass

        @service()
        async def kwargs_service(self, **kwargs: Any) -> None:  # Invalid: **kwargs
            pass

    # Test valid service
    validate_service_method_signature(ValidationPlugin.valid_service)

    # Test call signature
    call_sig = get_service_call_signature(ValidationPlugin.valid_service)
    params = list(call_sig.parameters.values())
    assert len(params) == 2
    assert params[0].name == "param1"
    assert params[1].name == "param2"
    assert params[1].default == 10

    # Test invalid services
    with pytest.raises(ServiceDefinitionError, match=r"\*args"):
        validate_service_method_signature(ValidationPlugin.varargs_service)

    with pytest.raises(ServiceDefinitionError, match=r"\*\*kwargs"):
        validate_service_method_signature(ValidationPlugin.kwargs_service)


@pytest.mark.asyncio
async def test_service_execution() -> None:
    """Test actual service method execution."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import extract_service_methods, service

    @plugin(name="execution_plugin")
    class ExecutionPlugin(PluginBase):
        def __init__(self, **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)
            self.counter = 0

        @service(name="increment")
        async def increment_counter(self, amount: int = 1) -> int:
            self.counter += amount
            return self.counter

        @service(name="get_info")
        async def get_plugin_info(self, include_counter: bool = True) -> dict[str, Any]:
            info: dict[str, Any] = {"name": "execution_plugin", "version": "1.0.0"}
            if include_counter:
                info["counter"] = self.counter
            return info

    # Create plugin instance
    plugin_instance = ExecutionPlugin()

    # Extract and call services
    services = extract_service_methods(plugin_instance)

    # Test increment service
    result = await services["increment"](5)
    assert result == 5
    assert plugin_instance.counter == 5

    # Test increment again
    result = await services["increment"](3)
    assert result == 8
    assert plugin_instance.counter == 8

    # Test get_info service
    info = await services["get_info"](include_counter=True)
    assert info["name"] == "execution_plugin"
    assert info["counter"] == 8

    # Test get_info without counter
    info = await services["get_info"](include_counter=False)
    assert "counter" not in info
