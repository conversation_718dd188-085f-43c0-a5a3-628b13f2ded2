# tests/unit/api/test_plugin_clean.py

"""
Clean pytest-compatible tests for the Plugin API.
"""

from __future__ import annotations

import os
import sys
from typing import Any

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_plugin_base() -> None:
    """Test PluginBase functionality."""
    from plugginger.api.plugin import PluginBase

    # Test basic instantiation
    plugin = PluginBase()
    assert not plugin.is_setup
    assert not plugin.is_torn_down

    # Test plugin instance ID handling
    with pytest.raises(RuntimeError, match="not been registered"):
        _ = plugin.plugin_instance_id

    # Test internal methods
    plugin._set_plugin_instance_id("test_plugin_001")
    assert plugin.plugin_instance_id == "test_plugin_001"

    plugin._mark_setup()
    assert plugin.is_setup

    plugin._mark_torn_down()
    assert plugin.is_torn_down


def test_plugin_decorator() -> None:
    """Test @plugin decorator functionality."""
    from plugginger.api.plugin import PluginBase, get_plugin_metadata, is_plugin_class, plugin

    # Test basic plugin decoration
    @plugin(name="test_plugin", version="1.2.3")
    class TestPlugin(PluginBase):
        pass

    # Test metadata extraction
    metadata = get_plugin_metadata(TestPlugin)
    assert metadata["name"] == "test_plugin"
    assert metadata["version"] == "1.2.3"
    assert metadata["dependencies"] == []
    assert metadata["optional_dependencies"] == []
    assert metadata["class_name"] == "TestPlugin"

    # Test is_plugin_class
    assert is_plugin_class(TestPlugin)

    # Test plugin with dependencies
    @plugin(
        name="advanced_plugin",
        version="2.0.0",
        dependencies=["database", "cache"],
        optional_dependencies=["metrics"]
    )
    class AdvancedPlugin(PluginBase):
        async def setup(self) -> None:
            self.initialized = True  # type: ignore

    metadata = get_plugin_metadata(AdvancedPlugin)
    assert metadata["name"] == "advanced_plugin"
    assert metadata["dependencies"] == ["database", "cache"]
    assert metadata["optional_dependencies"] == ["metrics"]

    # Test default name (class name)
    @plugin()
    class DefaultNamePlugin(PluginBase):
        pass

    metadata = get_plugin_metadata(DefaultNamePlugin)
    assert metadata["name"] == "DefaultNamePlugin"


def test_plugin_validation_invalid_base() -> None:
    """Test plugin validation for invalid base class."""
    from plugginger.api.plugin import plugin
    from plugginger.core.exceptions import PluginRegistrationError

    with pytest.raises(PluginRegistrationError, match="must inherit from PluginBase"):
        @plugin()
        class InvalidPlugin:  # type: ignore
            pass


def test_plugin_validation_non_plugin() -> None:
    """Test getting metadata from non-plugin class."""
    from plugginger.api.plugin import get_plugin_metadata, is_plugin_class
    from plugginger.core.exceptions import PluginRegistrationError

    class NonPlugin:
        pass

    with pytest.raises(PluginRegistrationError, match="not a valid plugin"):
        get_plugin_metadata(NonPlugin)  # type: ignore

    # Test is_plugin_class with non-plugin
    assert not is_plugin_class(NonPlugin)


@pytest.mark.asyncio
async def test_plugin_lifecycle() -> None:
    """Test plugin lifecycle methods."""
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="lifecycle_plugin")
    class LifecyclePlugin(PluginBase):
        def __init__(self, **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)
            self.setup_called = False
            self.teardown_called = False

        async def setup(self) -> None:
            self.setup_called = True

        async def teardown(self) -> None:
            self.teardown_called = True

    # Test lifecycle
    plugin_instance = LifecyclePlugin()

    # Test setup
    await plugin_instance.setup()
    assert plugin_instance.setup_called

    # Test teardown
    await plugin_instance.teardown()
    assert plugin_instance.teardown_called


def test_plugin_dependency_injection() -> None:
    """Test plugin dependency injection."""
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="di_plugin")
    class DIPlugin(PluginBase):
        def __init__(self, **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)

    # Test dependency injection
    mock_service = "mock_service_instance"
    mock_config = {"setting": "value"}

    plugin_instance = DIPlugin(
        service=mock_service,
        config=mock_config
    )

    # Check that dependencies were injected
    assert hasattr(plugin_instance, 'service')
    assert hasattr(plugin_instance, 'config')
    assert plugin_instance.service == mock_service  # type: ignore
    assert plugin_instance.config == mock_config  # type: ignore


def test_plugin_metadata_edge_cases() -> None:
    """Test plugin metadata edge cases."""
    from plugginger.api.plugin import PluginBase, get_plugin_metadata, plugin

    # Test plugin with empty dependencies
    @plugin(name="empty_deps", dependencies=[], optional_dependencies=[])
    class EmptyDepsPlugin(PluginBase):
        pass

    metadata = get_plugin_metadata(EmptyDepsPlugin)
    assert metadata["dependencies"] == []
    assert metadata["optional_dependencies"] == []

    # Test plugin with None version
    @plugin(name="no_version")
    class NoVersionPlugin(PluginBase):
        pass

    metadata = get_plugin_metadata(NoVersionPlugin)
    assert metadata["version"] == "1.0.0"  # Default version


def test_plugin_base_edge_cases() -> None:
    """Test PluginBase edge cases."""
    from plugginger.api.plugin import PluginBase

    plugin = PluginBase()

    # Test multiple ID setting
    plugin._set_plugin_instance_id("first_id")
    plugin._set_plugin_instance_id("second_id")  # Should overwrite
    assert plugin.plugin_instance_id == "second_id"

    # Test multiple setup/teardown marking
    plugin._mark_setup()
    plugin._mark_setup()  # Should be idempotent
    assert plugin.is_setup

    plugin._mark_torn_down()
    plugin._mark_torn_down()  # Should be idempotent
    assert plugin.is_torn_down


def test_plugin_inheritance() -> None:
    """Test plugin inheritance scenarios."""
    from plugginger.api.plugin import PluginBase, get_plugin_metadata, plugin

    @plugin(name="base_plugin")
    class BasePlugin(PluginBase):
        def base_method(self) -> str:
            return "base"

    @plugin(name="derived_plugin")
    class DerivedPlugin(BasePlugin):
        def derived_method(self) -> str:
            return "derived"

    # Both should be valid plugins
    base_metadata = get_plugin_metadata(BasePlugin)
    derived_metadata = get_plugin_metadata(DerivedPlugin)

    assert base_metadata["name"] == "base_plugin"
    assert derived_metadata["name"] == "derived_plugin"

    # Test instance creation
    base_instance = BasePlugin()
    derived_instance = DerivedPlugin()

    assert base_instance.base_method() == "base"
    assert derived_instance.base_method() == "base"
    assert derived_instance.derived_method() == "derived"


def test_plugin_with_custom_init() -> None:
    """Test plugin with custom __init__ method."""
    from plugginger.api.plugin import PluginBase, plugin

    @plugin(name="custom_init_plugin")
    class CustomInitPlugin(PluginBase):
        def __init__(self, custom_param: str = "default", **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)
            self.custom_param = custom_param

    # Test with custom parameter
    plugin_instance = CustomInitPlugin(custom_param="custom_value", injected_dep="injected")

    assert plugin_instance.custom_param == "custom_value"
    assert hasattr(plugin_instance, 'injected_dep')
    assert plugin_instance.injected_dep == "injected"  # type: ignore
