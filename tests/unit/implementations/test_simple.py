# tests/unit/implementations/test_simple.py

"""
Simple tests for implementation modules that work with the actual API.
"""

from __future__ import annotations

import os
import sys
from typing import Any, Protocol

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


class TestDIContainer:
    """Test DIContainer implementation."""

    def test_container_creation(self) -> None:
        """Test container creation."""
        from plugginger.implementations.container import DIContainer

        container = DIContainer()
        assert container is not None

    def test_register_instance(self) -> None:
        """Test instance registration."""
        from plugginger.implementations.container import DIContainer

        class TestInterface(Protocol):
            def get_value(self) -> str: ...

        class TestImplementation:
            def get_value(self) -> str:
                return "test_value"

        container = DIContainer()
        instance = TestImplementation()
        container.register_instance(TestInterface, instance)

        assert container.has(TestInterface)
        retrieved = container.get(TestInterface)
        assert retrieved is instance
        assert retrieved.get_value() == "test_value"

    def test_missing_interface(self) -> None:
        """Test getting missing interface."""
        from plugginger.implementations.container import DIContainer

        class MissingInterface(Protocol):
            def missing_method(self) -> None: ...

        container = DIContainer()
        assert not container.has(MissingInterface)

        with pytest.raises(ValueError, match="is not registered"):
            container.get(MissingInterface)

    def test_list_registrations(self) -> None:
        """Test listing registrations."""
        from plugginger.implementations.container import DIContainer

        class TestInterface(Protocol):
            def test_method(self) -> None: ...

        class TestImplementation:
            def test_method(self) -> None:
                pass

        container = DIContainer()
        instance = TestImplementation()
        container.register_instance(TestInterface, instance)

        registrations = container.list_registrations()
        assert "TestInterface" in registrations
        assert "TestImplementation (instance)" in registrations["TestInterface"]


class TestSimpleServiceDispatcher:
    """Test SimpleServiceDispatcher implementation."""

    def test_dispatcher_creation(self) -> None:
        """Test dispatcher creation."""
        from plugginger.implementations.services import SimpleServiceDispatcher

        dispatcher = SimpleServiceDispatcher()
        assert dispatcher is not None
        assert len(dispatcher.list_services()) == 0

    def test_add_service(self) -> None:
        """Test adding services."""
        from plugginger.implementations.services import SimpleServiceDispatcher

        dispatcher = SimpleServiceDispatcher()

        async def test_service(value: int) -> int:
            return value * 2

        dispatcher.add_service("test.service", test_service)

        assert dispatcher.has_service("test.service")
        assert "test.service" in dispatcher.list_services()

    @pytest.mark.asyncio
    async def test_call_service(self) -> None:
        """Test calling services."""
        from plugginger.implementations.services import SimpleServiceDispatcher

        dispatcher = SimpleServiceDispatcher()

        async def multiply_service(a: int, b: int) -> int:
            return a * b

        dispatcher.add_service("multiply", multiply_service)

        result = await dispatcher.call_service("multiply", 3, 4)
        assert result == 12

    @pytest.mark.asyncio
    async def test_call_nonexistent_service(self) -> None:
        """Test calling non-existent service."""
        from plugginger.core.exceptions import ServiceNotFoundError
        from plugginger.implementations.services import SimpleServiceDispatcher

        dispatcher = SimpleServiceDispatcher()

        with pytest.raises(ServiceNotFoundError, match="Service 'missing' not found"):
            await dispatcher.call_service("missing", "arg")


class TestSimpleEventDispatcher:
    """Test SimpleEventDispatcher implementation."""

    def test_dispatcher_creation(self) -> None:
        """Test event dispatcher creation."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler)
        assert dispatcher is not None
        assert len(dispatcher.list_patterns()) == 0

    def test_add_listener(self) -> None:
        """Test adding event listeners."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler)

        async def test_listener(event_data: dict[str, Any], event_type: str) -> None:
            pass

        dispatcher.add_listener("test.*", test_listener)

        patterns = dispatcher.list_patterns()
        assert "test.*" in patterns

    @pytest.mark.asyncio
    async def test_emit_event(self) -> None:
        """Test emitting events."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler)
        events_received = []

        async def test_listener(event_data: dict[str, Any], event_type: str) -> None:
            events_received.append((event_type, event_data))

        dispatcher.add_listener("user.created", test_listener)

        await dispatcher.emit_event("user.created", {"user_id": 123})

        # Give some time for async processing
        import asyncio
        await asyncio.sleep(0.01)

        assert len(events_received) == 1
        assert events_received[0] == ("user.created", {"user_id": 123})

    def test_remove_listener(self) -> None:
        """Test removing event listeners."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler)

        async def test_listener(event_data: dict[str, Any], event_type: str) -> None:
            pass

        dispatcher.add_listener("test.event", test_listener)
        assert "test.event" in dispatcher.list_patterns()

        # Remove listener
        result = dispatcher.remove_listener("test.event", test_listener)
        assert result is True

        # Try to remove again
        result = dispatcher.remove_listener("test.event", test_listener)
        assert result is False

    @pytest.mark.asyncio
    async def test_shutdown(self) -> None:
        """Test event dispatcher shutdown."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler)

        async def test_listener(event_data: dict[str, Any], event_type: str) -> None:
            pass

        dispatcher.add_listener("test.event", test_listener)
        assert len(dispatcher.list_patterns()) > 0

        await dispatcher.shutdown()
        # After shutdown, patterns should still exist but no new events should be processed


class TestEventFaultHandler:
    """Test SimpleEventFaultHandler implementation."""

    def test_fault_handler_creation(self) -> None:
        """Test fault handler creation."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        assert handler is not None

    def test_should_invoke(self) -> None:
        """Test should_invoke method."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)

        async def test_listener() -> None:
            pass

        # Initially should invoke
        assert handler.should_invoke(test_listener)

    def test_handle_error_log_and_continue(self) -> None:
        """Test error handling with LOG_AND_CONTINUE policy."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)

        # Should not raise exception
        handler.handle_error("test_listener", 123, ValueError("test error"))

    def test_handle_error_isolate_and_log(self) -> None:
        """Test error handling with ISOLATE_AND_LOG policy."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG)

        async def test_listener() -> None:
            pass

        listener_id = id(test_listener)

        # Initially should invoke
        assert handler.should_invoke(test_listener)

        # Handle error - should isolate listener
        handler.handle_error("test_listener", listener_id, ValueError("test error"))

        # Should no longer invoke
        assert not handler.should_invoke(test_listener)
