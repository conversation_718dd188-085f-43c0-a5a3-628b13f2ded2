# tests/unit/implementations/test_events.py

"""
Tests for the Event system implementations.
"""

import asyncio
import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_event_fault_handler():
    """Test event fault handler functionality."""
    try:
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        # Test LOG_AND_CONTINUE policy
        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)

        async def test_listener():
            pass

        # Should allow invocation initially
        assert handler.should_invoke(test_listener)

        # Handle error - should not raise
        handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should still allow invocation with LOG_AND_CONTINUE
        assert handler.should_invoke(test_listener)

        # Test ISOLATE_AND_LOG policy
        isolate_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG)

        # Should allow invocation initially
        assert isolate_handler.should_invoke(test_listener)

        # Handle error - should isolate listener
        isolate_handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should not allow invocation after isolation
        assert not isolate_handler.should_invoke(test_listener)

        # Test FAIL_FAST policy
        fail_fast_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.FAIL_FAST)

        try:
            fail_fast_handler.handle_error("test_listener", id(test_listener), ValueError("test error"))
            raise AssertionError("Should have raised EventListenerUnhandledError")
        except Exception as e:
            assert "Unhandled exception" in str(e)

        print("✅ Event fault handler tests passed!")
        return True

    except Exception as e:
        print(f"❌ Event fault handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_dispatcher():
    """Test event dispatcher functionality."""
    async def async_test():
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
            dispatcher = SimpleEventDispatcher(fault_handler, default_listener_timeout=1.0)

            # Track listener calls
            calls = []

            async def test_listener(event_data):
                calls.append(("test_listener", event_data))

            async def wildcard_listener(event_data):
                calls.append(("wildcard_listener", event_data))

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)
            dispatcher.add_listener("user.*", wildcard_listener)

            # Test event emission
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.1)

            # Both listeners should have been called
            assert len(calls) == 2
            assert ("test_listener", {"user_id": 123}) in calls
            assert ("wildcard_listener", {"user_id": 123}) in calls

            # Test pattern listing
            patterns = dispatcher.list_patterns()
            assert "user.created" in patterns
            assert "user.*" in patterns

            # Test listener removal
            removed = dispatcher.remove_listener("user.created", test_listener)
            assert removed

            # Clear calls and test again
            calls.clear()
            await dispatcher.emit_event("user.created", {"user_id": 456})
            await asyncio.sleep(0.1)

            # Only wildcard listener should have been called
            assert len(calls) == 1
            assert ("wildcard_listener", {"user_id": 456}) in calls

            # Test shutdown
            await dispatcher.shutdown()

            print("✅ Event dispatcher tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_registry():
    """Test event registry functionality."""
    async def async_test():
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
                SimpleEventRegistry,
            )

            # Create components
            fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
            dispatcher = SimpleEventDispatcher(fault_handler)
            registry = SimpleEventRegistry(dispatcher)

            # Track calls
            calls = []

            async def plugin_listener(event_data):
                calls.append(("plugin_listener", event_data))

            # Register listener through registry
            registry.register_listener(
                "test_plugin",
                "on_user_event",
                ["user.created", "user.updated"],
                plugin_listener
            )

            # Test event emission
            await dispatcher.emit_event("user.created", {"user_id": 123})
            await asyncio.sleep(0.1)

            assert len(calls) == 1
            assert ("plugin_listener", {"user_id": 123}) in calls

            # Test plugin listener tracking
            plugin_listeners = registry.get_plugin_listeners("test_plugin")
            assert len(plugin_listeners) == 2  # Two patterns
            assert ("user.created", plugin_listener) in plugin_listeners
            assert ("user.updated", plugin_listener) in plugin_listeners

            # Test unregistering plugin listeners
            removed_count = registry.unregister_plugin_listeners("test_plugin")
            assert removed_count == 2

            # Test that listeners are actually removed
            calls.clear()
            await dispatcher.emit_event("user.created", {"user_id": 456})
            await asyncio.sleep(0.1)

            assert len(calls) == 0  # No listeners should be called

            # Test shutdown
            await dispatcher.shutdown()

            print("✅ Event registry tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event registry test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_listener_timeout():
    """Test event listener timeout handling."""
    async def async_test():
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler with FAIL_FAST to test timeout propagation
            fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
            dispatcher = SimpleEventDispatcher(fault_handler, default_listener_timeout=0.1)

            # Create a slow listener
            async def slow_listener(event_data):
                await asyncio.sleep(0.2)  # Longer than timeout

            dispatcher.add_listener("test.timeout", slow_listener)

            # Emit event - should handle timeout gracefully
            await dispatcher.emit_event("test.timeout", {"test": "data"})
            await asyncio.sleep(0.3)  # Wait for timeout to occur

            # Test shutdown
            await dispatcher.shutdown()

            print("✅ Event listener timeout tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event listener timeout test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def run_all_tests():
    """Run all event system tests."""
    tests = [
        test_event_fault_handler,
        test_event_dispatcher,
        test_event_registry,
        test_event_listener_timeout,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All event system tests passed!")
    else:
        print("\n❌ Some event system tests failed!")
    exit(0 if success else 1)
