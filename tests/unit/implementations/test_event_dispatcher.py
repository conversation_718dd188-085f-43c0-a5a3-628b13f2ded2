# tests/unit/implementations/test_event_system.py

"""
Comprehensive tests for the Event system implementations.

This module tests the event dispatcher, fault handler, and registry components
of the Plugginger framework with full type safety and comprehensive coverage.
"""

from __future__ import annotations

import asyncio
import os
import sys
from typing import Any
from unittest.mock import Mock

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


class TestEventFaultHandler:
    """Test cases for SimpleEventFaultHandler."""

    def test_log_and_continue_policy(self) -> None:
        """Test LOG_AND_CONTINUE fault policy."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        mock_logger = Mock()
        handler = SimpleEventFaultHandler(
            EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
        )

        async def test_listener() -> None:
            pass

        # Should allow invocation initially
        assert handler.should_invoke(test_listener)

        # Handle error - should not raise
        handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should still allow invocation with LOG_AND_CONTINUE
        assert handler.should_invoke(test_listener)

        # Verify logging
        mock_logger.assert_called_with(
            "[EventFaultHandler] Listener 'test_listener' error: ValueError('test error')"
        )

    def test_isolate_and_log_policy(self) -> None:
        """Test ISOLATE_AND_LOG fault policy."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        mock_logger = Mock()
        handler = SimpleEventFaultHandler(
            EventListenerFaultPolicy.ISOLATE_AND_LOG, logger=mock_logger
        )

        async def test_listener() -> None:
            pass

        # Should allow invocation initially
        assert handler.should_invoke(test_listener)

        # Handle error - should isolate listener
        handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should not allow invocation after isolation
        assert not handler.should_invoke(test_listener)

        # Verify logging - should log both error and isolation
        mock_logger.assert_any_call(
            "[EventFaultHandler] Listener 'test_listener' error: ValueError('test error')"
        )
        mock_logger.assert_any_call(
            "[EventFaultHandler] Listener 'test_listener' isolated due to error"
        )

    def test_fail_fast_policy_with_exception(self) -> None:
        """Test FAIL_FAST policy with regular exception."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        mock_logger = Mock()
        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.FAIL_FAST, logger=mock_logger)

        async def test_listener() -> None:
            pass

        with pytest.raises(Exception) as exc_info:
            handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        assert "Unhandled exception" in str(exc_info.value)

    def test_fail_fast_policy_with_timeout(self) -> None:
        """Test FAIL_FAST policy with timeout error."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        mock_logger = Mock()
        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.FAIL_FAST, logger=mock_logger)

        async def test_listener() -> None:
            pass

        with pytest.raises(Exception) as exc_info:
            handler.handle_error("test_listener", id(test_listener), TimeoutError("timeout"))

        assert "timed out" in str(exc_info.value)

    def test_fault_handler_without_logger(self) -> None:
        """Test fault handler without logger."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)

        async def test_listener() -> None:
            pass

        # Should not raise even without logger
        handler.handle_error("test_listener", id(test_listener), ValueError("test error"))
        assert handler.should_invoke(test_listener)


class TestEventDispatcher:
    """Test cases for SimpleEventDispatcher."""

    @pytest.mark.asyncio
    async def test_basic_event_emission(self) -> None:
        """Test basic event emission and listener invocation."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(
            EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
        )
        dispatcher = SimpleEventDispatcher(
            fault_handler, logger=mock_logger, default_listener_timeout=1.0
        )

        calls: list[tuple[str, dict[str, Any], str | None]] = []

        async def test_listener(event_data: dict[str, Any], event_type: str | None = None) -> None:
            calls.append(("test_listener", event_data, event_type))

        # Register listener
        dispatcher.add_listener("user.created", test_listener)

        # Emit event
        await dispatcher.emit_event("user.created", {"user_id": 123})
        await asyncio.sleep(0.1)  # Allow async tasks to complete

        # Verify listener was called
        assert len(calls) == 1
        assert calls[0] == ("test_listener", {"user_id": 123}, "user.created")

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_wildcard_pattern_matching(self) -> None:
        """Test wildcard pattern matching."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        calls: list[tuple[str, dict[str, Any], str | None]] = []

        async def wildcard_listener(
            event_data: dict[str, Any], event_type: str | None = None
        ) -> None:
            calls.append(("wildcard_listener", event_data, event_type))

        # Register wildcard listener
        dispatcher.add_listener("user.*", wildcard_listener)

        # Test multiple matching events
        await dispatcher.emit_event("user.created", {"user_id": 123})
        await dispatcher.emit_event("user.updated", {"user_id": 456})
        await dispatcher.emit_event("order.created", {"order_id": 789})  # Should not match
        await asyncio.sleep(0.1)

        # Verify only matching events triggered listener
        assert len(calls) == 2
        assert ("wildcard_listener", {"user_id": 123}, "user.created") in calls
        assert ("wildcard_listener", {"user_id": 456}, "user.updated") in calls

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_listener_removal(self) -> None:
        """Test listener removal functionality."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        calls: list[str] = []

        async def test_listener(event_data: dict[str, Any]) -> None:
            calls.append("listener_called")

        # Register and then remove listener
        dispatcher.add_listener("test.event", test_listener)
        removed = dispatcher.remove_listener("test.event", test_listener)
        assert removed

        # Emit event - should not trigger removed listener
        await dispatcher.emit_event("test.event", {"data": "test"})
        await asyncio.sleep(0.1)

        assert len(calls) == 0

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_no_matching_listeners(self) -> None:
        """Test event emission with no matching listeners."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        # Emit event with no listeners
        await dispatcher.emit_event("no.listeners", {"data": "test"})
        await asyncio.sleep(0.1)

        # Verify logging
        mock_logger.assert_called_with(
            "[EventDispatcher] No active listeners for event 'no.listeners'"
        )

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_listener_timeout(self) -> None:
        """Test listener timeout handling."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(
            EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
        )
        dispatcher = SimpleEventDispatcher(
            fault_handler, logger=mock_logger, default_listener_timeout=0.1
        )

        async def slow_listener(event_data: dict[str, Any]) -> None:
            await asyncio.sleep(0.2)  # Longer than timeout

        dispatcher.add_listener("test.timeout", slow_listener)
        await dispatcher.emit_event("test.timeout", {"data": "test"})
        await asyncio.sleep(0.3)  # Wait for timeout and cleanup

        # Verify timeout was logged
        mock_logger.assert_any_call(
            "[EventFaultHandler] Listener 'TestEventDispatcher.test_listener_timeout.<locals>.slow_listener' error: TimeoutError()"
        )

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_listener_exception_handling(self) -> None:
        """Test listener exception handling."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(
            EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
        )
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        async def failing_listener(event_data: dict[str, Any]) -> None:
            raise ValueError("Test exception")

        dispatcher.add_listener("test.exception", failing_listener)
        await dispatcher.emit_event("test.exception", {"data": "test"})
        await asyncio.sleep(0.1)

        # Verify exception was logged
        mock_logger.assert_any_call(
            "[EventFaultHandler] Listener 'TestEventDispatcher.test_listener_exception_handling.<locals>.failing_listener' error: ValueError('Test exception')"
        )

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_shutdown_with_active_tasks(self) -> None:
        """Test shutdown behavior with active tasks."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        async def long_running_listener(event_data: dict[str, Any]) -> None:
            await asyncio.sleep(1.0)

        dispatcher.add_listener("test.long", long_running_listener)
        await dispatcher.emit_event("test.long", {"data": "test"})
        await asyncio.sleep(0.01)  # Let task start

        # Should have active tasks
        assert len(dispatcher._active_tasks) == 1

        # Shutdown should cancel active tasks
        await dispatcher.shutdown()
        assert len(dispatcher._active_tasks) == 0

    @pytest.mark.asyncio
    async def test_emit_after_shutdown(self) -> None:
        """Test event emission after shutdown."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        await dispatcher.shutdown()

        # Emit event after shutdown
        await dispatcher.emit_event("test.after.shutdown", {"data": "test"})

        # Verify event was ignored
        mock_logger.assert_called_with(
            "[EventDispatcher] Ignoring event 'test.after.shutdown' - shutting down"
        )

    def test_pattern_listing(self) -> None:
        """Test pattern listing functionality."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

        async def test_listener(event_data: dict[str, Any]) -> None:
            pass

        # Register listeners for different patterns
        dispatcher.add_listener("user.created", test_listener)
        dispatcher.add_listener("user.*", test_listener)
        dispatcher.add_listener("order.updated", test_listener)

        patterns = dispatcher.list_patterns()
        assert "user.created" in patterns
        assert "user.*" in patterns
        assert "order.updated" in patterns


class TestEventRegistry:
    """Test cases for SimpleEventRegistry."""

    @pytest.mark.asyncio
    async def test_plugin_listener_registration(self) -> None:
        """Test plugin listener registration and tracking."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
            SimpleEventRegistry,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
        registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

        calls: list[tuple[str, dict[str, Any]]] = []

        async def plugin_listener(event_data: dict[str, Any]) -> None:
            calls.append(("plugin_listener", event_data))

        # Register listener through registry
        registry.register_listener(
            "test_plugin", "on_user_event", ["user.created", "user.updated"], plugin_listener
        )

        # Test event emission
        await dispatcher.emit_event("user.created", {"user_id": 123})
        await asyncio.sleep(0.1)

        assert len(calls) == 1
        assert calls[0] == ("plugin_listener", {"user_id": 123})

        # Test plugin listener tracking
        plugin_listeners = registry.get_plugin_listeners("test_plugin")
        assert len(plugin_listeners) == 2  # Two patterns
        assert ("user.created", plugin_listener) in plugin_listeners
        assert ("user.updated", plugin_listener) in plugin_listeners

        await dispatcher.shutdown()

    @pytest.mark.asyncio
    async def test_plugin_listener_unregistration(self) -> None:
        """Test plugin listener unregistration."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
            SimpleEventRegistry,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
        registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

        calls: list[str] = []

        async def plugin_listener(event_data: dict[str, Any]) -> None:
            calls.append("listener_called")

        # Register listener
        registry.register_listener(
            "test_plugin", "on_user_event", ["user.created", "user.updated"], plugin_listener
        )

        # Unregister plugin listeners
        removed_count = registry.unregister_plugin_listeners("test_plugin")
        assert removed_count == 2

        # Test that listeners are actually removed
        await dispatcher.emit_event("user.created", {"user_id": 456})
        await asyncio.sleep(0.1)

        assert len(calls) == 0  # No listeners should be called

        await dispatcher.shutdown()

    def test_get_plugin_listeners_empty(self) -> None:
        """Test get_plugin_listeners when no listeners are registered."""
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import (
            SimpleEventDispatcher,
            SimpleEventFaultHandler,
            SimpleEventRegistry,
        )

        mock_logger = Mock()
        fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
        dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
        registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

        # Get listeners for a non-existent plugin
        plugin_listeners = registry.get_plugin_listeners("non_existent_plugin")
        assert plugin_listeners == []
