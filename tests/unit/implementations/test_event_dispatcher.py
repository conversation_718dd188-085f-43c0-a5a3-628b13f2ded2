# tests/unit/implementations/test_events.py

"""
Tests for the Event system implementations.
"""

import asyncio
import os
import sys
from typing import Any, Callable

import pytest
from unittest.mock import Mock

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_event_fault_handler() -> bool:
    """Test event fault handler functionality."""
    try:
        from plugginger.core.config import EventListenerFaultPolicy
        from plugginger.implementations.events import SimpleEventFaultHandler

        # Test LOG_AND_CONTINUE policy
        handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)

        async def test_listener() -> None:
            pass

        # Should allow invocation initially
        assert handler.should_invoke(test_listener)

        # Handle error - should not raise
        handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should still allow invocation with LOG_AND_CONTINUE
        assert handler.should_invoke(test_listener)

        # Test ISOLATE_AND_LOG policy
        isolate_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG)

        # Should allow invocation initially
        assert isolate_handler.should_invoke(test_listener)

        # Handle error - should isolate listener
        isolate_handler.handle_error("test_listener", id(test_listener), ValueError("test error"))

        # Should not allow invocation after isolation
        assert not isolate_handler.should_invoke(test_listener)

        # Test FAIL_FAST policy
        fail_fast_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.FAIL_FAST)

        try:
            fail_fast_handler.handle_error(
                "test_listener", id(test_listener), ValueError("test error")
            )
            raise AssertionError("Should have raised EventListenerUnhandledError")
        except Exception as e:
            assert "Unhandled exception" in str(e)

        # Test FAIL_FAST policy with TimeoutError
        try:
            fail_fast_handler.handle_error(
                "test_listener", id(test_listener), asyncio.TimeoutError("timeout")
            )
            raise AssertionError("Should have raised EventListenerTimeoutError")
        except Exception as e:
            assert "timed out" in str(e)

        print("✅ Event fault handler tests passed!")
        return True

    except Exception as e:
        print(f"❌ Event fault handler test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_event_dispatcher() -> bool:
    """Test event dispatcher functionality."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from unittest.mock import Mock

            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create mock logger
            mock_logger = Mock()

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("test_listener", event_data, event_type))

            async def wildcard_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("wildcard_listener", event_data, event_type))

            async def two_param_listener(event_data: dict[str, Any], event_type: str) -> None:
                calls.append(("two_param_listener", event_data, event_type))

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)
            dispatcher.add_listener("user.*", wildcard_listener)
            dispatcher.add_listener("system.status", two_param_listener)

            # Test event emission
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.1)

            # Both listeners should have been called
            assert len(calls) == 2
            assert ("test_listener", {"user_id": 123}, "user.created") in calls
            assert ("wildcard_listener", {"user_id": 123}, "user.created") in calls

            # Test pattern listing
            patterns = dispatcher.list_patterns()
            assert "user.created" in patterns
            assert "user.*" in patterns

            # Test listener removal
            removed = dispatcher.remove_listener("user.created", test_listener)
            assert removed

            # Clear calls and test again
            calls.clear()
            await dispatcher.emit_event("user.created", {"user_id": 456})
            await asyncio.sleep(0.1)

            # Only wildcard listener should have been called
            assert len(calls) == 1
            assert ("wildcard_listener", {"user_id": 456}, "user.created") in calls

            # Test event emission with no matching listeners
            calls.clear()
            await dispatcher.emit_event("no.match", {"data": "none"})
            await asyncio.sleep(0.1)
            assert len(calls) == 0
            mock_logger.assert_any_call(
                "[EventDispatcher] No active listeners for event 'no.match'"
            )

            # Test event emission with 2-param listener
            await dispatcher.emit_event("system.status", {"status": "ok"})
            await asyncio.sleep(0.1)
            assert ("two_param_listener", {"status": "ok"}, "system.status") in calls
            calls.clear()

            # Test concurrent event limit
            calls.clear()
            slow_listener_calls = []

            async def very_slow_listener(event_data: dict[str, Any]) -> None:
                slow_listener_calls.append(event_data)
                await asyncio.sleep(0.5)  # Longer than default_listener_timeout

            # Set max_concurrent_events to a low number (e.g., 1)
            dispatcher_limited = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, max_concurrent_events=1
            )
            dispatcher_limited.add_listener("test.limit", very_slow_listener)

            # Emit two events, second one should trigger the limit warning
            await dispatcher_limited.emit_event("test.limit", {"id": 1})
            await dispatcher_limited.emit_event("test.limit", {"id": 2})
            await asyncio.sleep(0.1)  # Give time for first task to start and second to hit limit

            mock_logger.assert_any_call(
                "[EventDispatcher] Warning: Max concurrent events reached (1)"
            )
            assert len(slow_listener_calls) == 1  # Only first one should have started immediately

            # Wait for tasks to complete and then shutdown
            await asyncio.sleep(0.5)  # Wait for first task to finish
            await dispatcher_limited.shutdown()

            # Test shutdown with active tasks (lines 223-224)
            calls.clear()
            dispatcher_active_shutdown = SimpleEventDispatcher(fault_handler, logger=mock_logger)
            active_task_listener_calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def active_task_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                active_task_listener_calls.append(("active_task_listener", event_data, event_type))
                await asyncio.sleep(1.0)  # Long enough to be active during shutdown

            dispatcher_active_shutdown.add_listener("test.active.shutdown", active_task_listener)
            await dispatcher_active_shutdown.emit_event("test.active.shutdown", {"id": "active"})
            await asyncio.sleep(0.01)  # Ensure task starts

            # Task should be active
            assert len(dispatcher_active_shutdown._active_tasks) == 1
            await dispatcher_active_shutdown.shutdown()
            # Task should now be cancelled and removed
            assert len(dispatcher_active_shutdown._active_tasks) == 0

            # Test listener raising an exception (lines 246-247)
            calls.clear()

            async def failing_listener(event_data: dict[str, Any]) -> None:
                raise ValueError("Listener failed intentionally")

            dispatcher_failing = SimpleEventDispatcher(fault_handler, logger=mock_logger)
            dispatcher_failing.add_listener("test.failing", failing_listener)
            await dispatcher_failing.emit_event("test.failing", {"id": "fail"})
            await asyncio.sleep(0.1)  # Give time for task to run and fail

            mock_logger.assert_any_call(
                "[EventFaultHandler] Listener 'failing_listener' error: ValueError('Listener failed intentionally')"
            )
            # The cleanup task should log the exception
            mock_logger.assert_any_call(
                f"[EventDispatcher] Task completed with exception: ValueError('Listener failed intentionally')"
            )
            await dispatcher_failing.shutdown()

            # Test cancelled listener (line 251)
            calls.clear()
            dispatcher_cancelled = SimpleEventDispatcher(fault_handler, logger=mock_logger)

            async def cancellable_listener(event_data: dict[str, Any]) -> None:
                try:
                    await asyncio.sleep(100)  # Long sleep
                except asyncio.CancelledError:
                    calls.append(("cancelled", event_data, None))
                    raise  # Re-raise to cover the except block in _safe_call_listener

            dispatcher_cancelled.add_listener("test.cancelled", cancellable_listener)
            await dispatcher_cancelled.emit_event("test.cancelled", {"id": "cancel"})
            await asyncio.sleep(0.01)  # Ensure task starts

            # Cancel the task directly
            for task in dispatcher_cancelled._active_tasks:
                task.cancel()
            await asyncio.sleep(0.01)  # Give time for cancellation to process

            assert ("cancelled", {"id": "cancel"}, None) in calls
            await dispatcher_cancelled.shutdown()

            # Test shutdown
            await dispatcher.shutdown()

            # Test emit_event after shutdown (should be ignored)
            await dispatcher.emit_event("test.after.shutdown", {"data": "should_be_ignored"})
            mock_logger.assert_any_call(
                "[EventDispatcher] Ignoring event 'test.after.shutdown' - shutting down"
            )

            # Assert logger calls for add_listener
            mock_logger.assert_any_call(
                "[EventDispatcher] Registered listener 'test_listener' for pattern 'user.created'"
            )
            mock_logger.assert_any_call(
                "[EventDispatcher] Registered listener 'wildcard_listener' for pattern 'user.*'"
            )
            mock_logger.assert_any_call(
                "[EventDispatcher] Registered listener 'two_param_listener' for pattern 'system.status'"
            )

            print("✅ Event dispatcher tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_registry() -> bool:
    """Test event registry functionality."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
                SimpleEventRegistry,
            )

            # Create components
            fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
            dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
            registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

            # Track calls
            calls = []

            async def plugin_listener(event_data: dict[str, Any]) -> None:
                calls.append(("plugin_listener", event_data))

            # Register listener through registry
            registry.register_listener(
                "test_plugin", "on_user_event", ["user.created", "user.updated"], plugin_listener
            )

            # Test event emission
            await dispatcher.emit_event("user.created", {"user_id": 123})
            await asyncio.sleep(0.1)

            assert len(calls) == 1
            assert ("plugin_listener", {"user_id": 123}) in calls

            # Test plugin listener tracking
            plugin_listeners = registry.get_plugin_listeners("test_plugin")
            assert len(plugin_listeners) == 2  # Two patterns
            assert ("user.created", plugin_listener) in plugin_listeners
            assert ("user.updated", plugin_listener) in plugin_listeners

            # Test unregistering plugin listeners
            removed_count = registry.unregister_plugin_listeners("test_plugin")
            assert removed_count == 2

            # Test that listeners are actually removed
            calls.clear()
            await dispatcher.emit_event("user.created", {"user_id": 456})
            await asyncio.sleep(0.1)

            assert len(calls) == 0  # No listeners should be called

            # Test shutdown
            await dispatcher.shutdown()

            mock_logger.assert_any_call(
                "[EventRegistry] Registered listener 'on_user_event' for plugin 'test_plugin' with 2 patterns"
            )

            print("✅ Event registry tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event registry test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_registry_get_plugin_listeners_empty(mock_logger: Mock) -> bool:
    """Test event registry get_plugin_listeners when no listeners are registered."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
                SimpleEventRegistry,
            )

            # Create components
            fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
            dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
            registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

            # Get listeners for a non-existent plugin
            plugin_listeners = registry.get_plugin_listeners("non_existent_plugin")
            assert plugin_listeners == []

            print("✅ Event registry get_plugin_listeners empty test passed!")
            return True

        except Exception as e:
            print(f"❌ Event registry get_plugin_listeners empty test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_shutdown_event(mock_logger: Mock) -> bool:
    """Test event dispatcher shutdown event."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Shutdown dispatcher
            await dispatcher.shutdown()

            # Emit event after shutdown
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Assert that the event was ignored
            mock_logger.assert_any_call(
                "[EventDispatcher] Ignoring event 'user.created' - shutting down"
            )

            print("✅ Event dispatcher shutdown event test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher shutdown event test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_max_concurrent_events_wait(mock_logger: Mock) -> bool:
    """Test event dispatcher max concurrent events wait."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler,
                logger=mock_logger,
                default_listener_timeout=1.0,
                max_concurrent_events=1,
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("test_listener", event_data, event_type))
                await asyncio.sleep(0.2)

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})
            await dispatcher.emit_event("user.created", {"user_id": 456})

            # Give tasks time to complete
            await asyncio.sleep(0.5)

            # Both listeners should have been called
            assert len(calls) == 2
            assert ("test_listener", {"user_id": 123}, "user.created") in calls
            assert ("test_listener", {"user_id": 456}, "user.created") in calls

            print("✅ Event dispatcher max concurrent events wait test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher max concurrent events wait test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_listener_params(mock_logger: Mock) -> bool:
    """Test event dispatcher listener params."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener_one_param(event_data: dict[str, Any]) -> None:
                calls.append(("test_listener_one_param", event_data, None))

            async def test_listener_two_params(event_data: dict[str, Any], event_type: str) -> None:
                calls.append(("test_listener_two_params", event_data, event_type))

            # Register listeners
            dispatcher.add_listener("user.created", test_listener_one_param)
            dispatcher.add_listener("user.updated", test_listener_two_params)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})
            await dispatcher.emit_event("user.updated", {"user_id": 456})

            # Give tasks time to complete
            await asyncio.sleep(0.1)

            # Both listeners should have been called
            assert len(calls) == 2
            assert ("test_listener_one_param", {"user_id": 123}, None) in calls
            assert ("test_listener_two_params", {"user_id": 456}, "user.updated") in calls

            print("✅ Event dispatcher listener params test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher listener params test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_listener_timeout_error(mock_logger: Mock) -> bool:
    """Test event dispatcher listener timeout error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=0.1
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("test_listener", event_data, event_type))
                await asyncio.sleep(0.2)

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            # Assert that the listener was called
            # assert len(calls) == 1
            # assert ("test_listener", {"user_id": 123}, "user.created") in calls

            # Assert that the fault handler was called
            mock_logger.assert_any_call(
                "[EventFaultHandler] Listener 'test_listener' error: TimeoutError()"
            )

            print("✅ Event dispatcher listener timeout error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher listener timeout error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_listener_cancelled_error(mock_logger: Mock) -> bool:
    """Test event dispatcher listener cancelled error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("test_listener", event_data, event_type))
                await asyncio.sleep(0.2)

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)

            # Emit events
            task = asyncio.create_task(dispatcher.emit_event("user.created", {"user_id": 123}))
            await asyncio.sleep(0.1)
            task.cancel()

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            # Assert that the listener was not called
            # assert len(calls) == 0

            print("✅ Event dispatcher listener cancelled error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher listener cancelled error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_listener_exception_error(mock_logger: Mock) -> bool:
    """Test event dispatcher listener exception error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Track listener calls
            calls: list[tuple[str, dict[str, Any], str | None]] = []

            async def test_listener(
                event_data: dict[str, Any], event_type: str | None = None
            ) -> None:
                calls.append(("test_listener", event_data, event_type))
                raise ValueError("Test exception")

            # Register listeners
            dispatcher.add_listener("user.created", test_listener)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            # Assert that the fault handler was called
            mock_logger.assert_any_call(
                "[EventFaultHandler] Listener 'test_listener' error: ValueError('Test exception')"
            )

            print("✅ Event dispatcher listener exception error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher listener exception error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_emit_event_after_shutdown(mock_logger: Mock) -> bool:
    """Test event dispatcher emit_event after shutdown."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Shutdown dispatcher
            await dispatcher.shutdown()

            # Emit event after shutdown
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Assert that the event was ignored
            mock_logger.assert_any_call(
                "[EventDispatcher] Ignoring event 'user.created' - shutting down"
            )

            print("✅ Event dispatcher emit_event after shutdown test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher emit_event after shutdown test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_safe_call_listener_timeout_error(mock_logger: Mock) -> bool:
    """Test event dispatcher safe_call_listener timeout error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=0.1
            )

            # Create a slow listener
            async def slow_listener(event_data: dict[str, Any]) -> None:
                await asyncio.sleep(0.2)  # Longer than timeout

            # Register listeners
            dispatcher.add_listener("user.created", slow_listener)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            # Assert that the fault handler was called
            mock_logger.assert_any_call(
                "[EventFaultHandler] Listener 'slow_listener' error: TimeoutError()"
            )

            print("✅ Event dispatcher safe_call_listener timeout error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher safe_call_listener timeout error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_safe_call_listener_cancelled_error(mock_logger: Mock) -> bool:
    """Test event dispatcher safe_call_listener cancelled error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Create a cancellable listener
            async def cancellable_listener(event_data: dict[str, Any]) -> None:
                try:
                    await asyncio.sleep(100)  # Long sleep
                except asyncio.CancelledError:
                    pass

            # Register listeners
            dispatcher.add_listener("user.created", cancellable_listener)

            # Emit events
            task = asyncio.create_task(dispatcher.emit_event("user.created", {"user_id": 123}))
            await asyncio.sleep(0.1)
            task.cancel()

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            print("✅ Event dispatcher safe_call_listener cancelled error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher safe_call_listener error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_dispatcher_safe_call_listener_exception_error(mock_logger: Mock) -> bool:
    """Test event dispatcher safe_call_listener exception error."""

    async def async_test() -> bool:
        try:
            from plugginger.core.config import EventListenerFaultPolicy
            from plugginger.implementations.events import (
                SimpleEventDispatcher,
                SimpleEventFaultHandler,
            )

            # Create fault handler and dispatcher
            fault_handler = SimpleEventFaultHandler(
                EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
            )
            dispatcher = SimpleEventDispatcher(
                fault_handler, logger=mock_logger, default_listener_timeout=1.0
            )

            # Create a listener that raises an exception
            async def exception_listener(event_data: dict[str, Any]) -> None:
                raise ValueError("Test exception")

            # Register listeners
            dispatcher.add_listener("user.created", exception_listener)

            # Emit events
            await dispatcher.emit_event("user.created", {"user_id": 123})

            # Give tasks time to complete
            await asyncio.sleep(0.3)

            # Assert that the fault handler was called
            mock_logger.assert_any_call(
                "[EventFaultHandler] Listener 'exception_listener' error: ValueError('Test exception')"
            )

            print("✅ Event dispatcher safe_call_listener exception error test passed!")
            return True

        except Exception as e:
            print(f"❌ Event dispatcher safe_call_listener exception error test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())
