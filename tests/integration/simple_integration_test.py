"""Simple integration test to verify basic functionality."""

from typing import Any

import pytest

from plugginger.api import PluginBase, plugin, service


@plugin(name="test_plugin")
class TestPlugin(PluginBase):
    """Simple test plugin."""

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.value = 0

    @service()
    async def increment(self) -> int:
        """Increment value."""
        self.value += 1
        return self.value

    @service()
    async def get_value(self) -> int:
        """Get current value."""
        return self.value


class TestSimpleIntegration:
    """Test simple integration."""

    def test_plugin_creation(self) -> None:
        """Test that plugin can be created."""
        plugin_instance = TestPlugin()
        assert plugin_instance.value == 0

    @pytest.mark.asyncio
    async def test_service_calls(self) -> None:
        """Test service calls work."""
        plugin_instance = TestPlugin()

        # Test increment
        result = await plugin_instance.increment()
        assert result == 1
        assert plugin_instance.value == 1

        # Test get_value
        value = await plugin_instance.get_value()
        assert value == 1

    def test_plugin_metadata(self) -> None:
        """Test plugin metadata."""
        from plugginger.api.plugin import get_plugin_metadata

        metadata = get_plugin_metadata(TestPlugin)
        assert metadata["name"] == "test_plugin"

    def test_service_extraction(self) -> None:
        """Test service extraction."""
        from plugginger.api.service import extract_service_methods

        plugin_instance = TestPlugin()
        services = extract_service_methods(plugin_instance)

        assert "increment" in services
        assert "get_value" in services
        assert len(services) == 2
