"""Test simple event system integration."""

from typing import Any

import pytest

from plugginger.api import PluggingerAppBuilder, PluginBase, on_event, plugin, service


@plugin(name="publisher")
class PublisherPlugin(PluginBase):
    """Plugin that publishes events."""

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.published_events: list[str] = []

    @service()
    async def publish_message(self, message: str) -> None:
        """Publish a message event."""
        self.published_events.append(message)
        # Note: In real implementation, self.app would be available
        # For now, we'll test the plugin structure

    @service()
    async def get_published_events(self) -> list[str]:
        """Get all published events."""
        return self.published_events.copy()


@plugin(name="subscriber")
class SubscriberPlugin(PluginBase):
    """Plugin that subscribes to events."""

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.received_events: list[dict[str, Any]] = []

    @on_event("message.sent")
    async def on_message_sent(self, event_data: dict[str, Any]) -> None:
        """Handle message sent event."""
        self.received_events.append(
            {"type": "message.sent", "data": event_data, "timestamp": "2024-01-01T00:00:00Z"}
        )

    @on_event("user.*")
    async def on_user_event(self, event_data: dict[str, Any], event_type: str) -> None:
        """Handle any user event."""
        self.received_events.append(
            {"type": event_type, "data": event_data, "timestamp": "2024-01-01T00:00:00Z"}
        )

    @service()
    async def get_received_events(self) -> list[dict[str, Any]]:
        """Get all received events."""
        return self.received_events.copy()

    @service()
    async def clear_events(self) -> None:
        """Clear all received events."""
        self.received_events.clear()


class TestSimpleEventFlow:
    """Test simple event flow between plugins."""

    def test_plugin_creation(self) -> None:
        """Test that plugins can be created and registered."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(PublisherPlugin)
            .register_plugin(SubscriberPlugin)
            .build()
        )

        assert app.app_name == "PluggingerApp"  # Default name

    def test_service_registration(self) -> None:
        """Test that services are properly registered."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(PublisherPlugin)
            .register_plugin(SubscriberPlugin)
            .build()
        )

        # Check that services are available
        services = app.list_services()

        expected_services = [
            "publisher.publish_message",
            "publisher.get_published_events",
            "subscriber.get_received_events",
            "subscriber.clear_events",
        ]

        for service_name in expected_services:
            assert service_name in services

    @pytest.mark.asyncio
    async def test_basic_service_calls(self) -> None:
        """Test basic service calls work."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(PublisherPlugin)
            .register_plugin(SubscriberPlugin)
            .build()
        )

        # Test publisher service
        await app.call_service("publisher.publish_message", message="Hello World")

        published = await app.call_service("publisher.get_published_events")
        assert len(published) == 1
        assert published[0] == "Hello World"

        # Test subscriber service
        received = await app.call_service("subscriber.get_received_events")
        assert isinstance(received, list)

        # Clear events
        await app.call_service("subscriber.clear_events")
        cleared = await app.call_service("subscriber.get_received_events")
        assert len(cleared) == 0

    def test_event_listener_registration(self) -> None:
        """Test that event listeners are properly registered."""
        app = PluggingerAppBuilder().register_plugin(SubscriberPlugin).build()

        # Check that event patterns are registered
        patterns = app.list_event_patterns()

        assert "message.sent" in patterns
        assert "user.*" in patterns

    @pytest.mark.asyncio
    async def test_multiple_plugins_interaction(self) -> None:
        """Test interaction between multiple plugins."""

        @plugin(name="counter")
        class CounterPlugin(PluginBase):
            def __init__(self, **injected_dependencies: Any) -> None:
                super().__init__(**injected_dependencies)
                self.count = 0

            @service()
            async def increment(self) -> int:
                """Increment counter."""
                self.count += 1
                return self.count

            @service()
            async def get_count(self) -> int:
                """Get current count."""
                return self.count

        app = (
            PluggingerAppBuilder()
            .register_plugin(PublisherPlugin)
            .register_plugin(SubscriberPlugin)
            .register_plugin(CounterPlugin)
            .build()
        )

        # Test all plugins work together
        await app.call_service("publisher.publish_message", message="Test")
        count = await app.call_service("counter.increment")
        assert count == 1

        published = await app.call_service("publisher.get_published_events")
        assert len(published) == 1

        final_count = await app.call_service("counter.get_count")
        assert final_count == 1

    def test_builder_fluent_interface(self) -> None:
        """Test builder fluent interface."""
        app = (
            PluggingerAppBuilder()
            .with_name("TestEventApp")
            .register_plugin(PublisherPlugin)
            .register_plugin(SubscriberPlugin)
            .build()
        )

        assert app.app_name == "TestEventApp"

        services = app.list_services()
        assert len(services) >= 4  # At least 4 services from 2 plugins

    def test_plugin_metadata(self) -> None:
        """Test plugin metadata is preserved."""
        from plugginger.api.plugin import get_plugin_metadata

        # Test publisher metadata
        pub_metadata = get_plugin_metadata(PublisherPlugin)
        assert pub_metadata["name"] == "publisher"

        # Test subscriber metadata
        sub_metadata = get_plugin_metadata(SubscriberPlugin)
        assert sub_metadata["name"] == "subscriber"

    @pytest.mark.asyncio
    async def test_error_handling_in_services(self) -> None:
        """Test error handling in service calls."""
        app = PluggingerAppBuilder().register_plugin(PublisherPlugin).build()

        # Test calling non-existent service
        from plugginger.core.exceptions import ServiceNotFoundError

        with pytest.raises(ServiceNotFoundError):
            await app.call_service("nonexistent.service")

        # Test calling service with wrong parameters
        # ServiceExecutionError wraps the original TypeError
        from plugginger.core.exceptions import ServiceExecutionError

        with pytest.raises(ServiceExecutionError):
            await app.call_service("publisher.publish_message", wrong_param="test")

    def test_app_instance_properties(self) -> None:
        """Test app instance properties."""
        app = (
            PluggingerAppBuilder()
            .with_name("PropertyTestApp")
            .register_plugin(PublisherPlugin)
            .build()
        )

        assert app.app_name == "PropertyTestApp"
        assert hasattr(app, "call_service")
        assert hasattr(app, "emit_event")
        assert hasattr(app, "list_services")
        assert hasattr(app, "list_event_patterns")
