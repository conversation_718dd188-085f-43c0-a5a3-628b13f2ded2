"""Integration tests for Depends and DI Container interaction."""

import pytest
from typing import Protocol

from plugginger.api.depends import Depends, inject_dependencies
from plugginger.implementations.container import DIContainer, set_container, reset_container


# Test interfaces and implementations
class DatabaseInterface(Protocol):
    """Database interface for testing."""
    
    def query(self, sql: str) -> str:
        """Execute a query."""
        ...


class LoggerInterface(Protocol):
    """Logger interface for testing."""
    
    def log(self, message: str) -> None:
        """Log a message."""
        ...


class TestDatabase:
    """Test database implementation."""
    
    def __init__(self) -> None:
        """Initialize test database."""
        self.queries_executed = 0
    
    def query(self, sql: str) -> str:
        """Execute a query."""
        self.queries_executed += 1
        return f"Result for: {sql} (query #{self.queries_executed})"


class TestLogger:
    """Test logger implementation."""
    
    def __init__(self) -> None:
        """Initialize test logger."""
        self.logged_messages: list[str] = []
    
    def log(self, message: str) -> None:
        """Log a message."""
        self.logged_messages.append(message)


class TestDependsDIIntegration:
    """Test integration between Depends and DI Container."""

    def setup_method(self) -> None:
        """Set up test environment."""
        # Reset container for each test
        reset_container()

    def teardown_method(self) -> None:
        """Clean up test environment."""
        # Reset container after each test
        reset_container()

    def test_depends_resolves_from_di_container(self) -> None:
        """Test that Depends can resolve dependencies from DI container."""
        container = DIContainer()
        set_container(container)
        
        # Register services in container
        container.register(DatabaseInterface, TestDatabase)
        container.register(LoggerInterface, TestLogger)
        
        # Create Depends markers
        db_depends = Depends(DatabaseInterface)
        logger_depends = Depends(LoggerInterface)
        
        # Resolve dependencies
        db_instance = db_depends.resolve("database")
        logger_instance = logger_depends.resolve("logger")
        
        # Verify instances
        assert isinstance(db_instance, TestDatabase)
        assert isinstance(logger_instance, TestLogger)
        
        # Test functionality
        result = db_instance.query("SELECT * FROM users")
        assert "Result for: SELECT * FROM users" in result
        assert db_instance.queries_executed == 1
        
        logger_instance.log("Test message")
        assert "Test message" in logger_instance.logged_messages

    def test_depends_singleton_behavior(self) -> None:
        """Test that Depends respects singleton behavior from DI container."""
        container = DIContainer()
        set_container(container)
        
        # Register as singleton (default)
        container.register(DatabaseInterface, TestDatabase, singleton=True)
        
        # Create multiple Depends markers
        db_depends1 = Depends(DatabaseInterface)
        db_depends2 = Depends(DatabaseInterface)
        
        # Resolve multiple times
        db1 = db_depends1.resolve("db1")
        db2 = db_depends2.resolve("db2")
        
        # Should be the same instance
        assert db1 is db2
        
        # Test shared state
        db1.query("SELECT 1")
        db2.query("SELECT 2")
        assert db1.queries_executed == 2
        assert db2.queries_executed == 2

    def test_depends_non_singleton_behavior(self) -> None:
        """Test that Depends respects non-singleton behavior from DI container."""
        container = DIContainer()
        set_container(container)
        
        # Register as non-singleton
        container.register(DatabaseInterface, TestDatabase, singleton=False)
        
        # Create Depends markers
        db_depends = Depends(DatabaseInterface)
        
        # Resolve multiple times
        db1 = db_depends.resolve("db1")
        db2 = db_depends.resolve("db2")
        
        # Should be different instances
        assert db1 is not db2
        assert isinstance(db1, TestDatabase)
        assert isinstance(db2, TestDatabase)
        
        # Test separate state
        db1.query("SELECT 1")
        db2.query("SELECT 2")
        assert db1.queries_executed == 1
        assert db2.queries_executed == 1

    def test_inject_dependencies_with_di_container(self) -> None:
        """Test inject_dependencies function with DI container."""
        container = DIContainer()
        set_container(container)
        
        # Register services
        container.register(DatabaseInterface, TestDatabase)
        container.register(LoggerInterface, TestLogger)
        
        # Define a function with dependencies
        def test_function(
            user_id: int,
            database: DatabaseInterface = Depends(DatabaseInterface),
            logger: LoggerInterface = Depends(LoggerInterface),
            optional_service: str = Depends("non.existent", optional=True, default="default_value")
        ) -> dict[str, str]:
            """Test function with dependencies."""
            result = database.query(f"SELECT * FROM users WHERE id = {user_id}")
            logger.log(f"Queried user {user_id}")
            return {"result": result, "optional": optional_service}
        
        # Inject dependencies
        injected = inject_dependencies(test_function)
        
        # Verify injected dependencies
        assert "database" in injected
        assert "logger" in injected
        assert "optional_service" in injected
        
        assert isinstance(injected["database"], TestDatabase)
        assert isinstance(injected["logger"], TestLogger)
        assert injected["optional_service"] == "default_value"
        
        # Test that the injected instances work
        db = injected["database"]
        logger = injected["logger"]
        
        result = db.query("TEST")
        logger.log("TEST MESSAGE")
        
        assert "Result for: TEST" in result
        assert "TEST MESSAGE" in logger.logged_messages

    def test_mixed_dependency_sources(self) -> None:
        """Test mixing DI container dependencies with provided dependencies."""
        container = DIContainer()
        set_container(container)
        
        # Register only database in container
        container.register(DatabaseInterface, TestDatabase)
        
        # Define function with mixed dependencies
        def test_function(
            database: DatabaseInterface = Depends(DatabaseInterface),
            config: dict[str, str] = Depends("app.config", optional=True, default={"env": "test"}),
            manual_param: str = "manual_value"
        ) -> dict[str, str]:
            """Test function with mixed dependencies."""
            return {"db_result": database.query("SELECT 1"), "config": str(config)}
        
        # Provide some dependencies manually
        provided_deps = {
            "manual_param": "provided_manually"
        }
        
        # Inject dependencies
        injected = inject_dependencies(test_function, **provided_deps)
        
        # Verify results
        assert "database" in injected
        assert "config" in injected
        assert "manual_param" in injected
        
        assert isinstance(injected["database"], TestDatabase)
        assert injected["config"] == {"env": "test"}  # Default value used
        assert injected["manual_param"] == "provided_manually"

    def test_dependency_chain_through_depends(self) -> None:
        """Test that dependency chains work through Depends resolution."""
        container = DIContainer()
        set_container(container)
        
        # Create a service that depends on other services
        class UserService:
            def __init__(self, database: DatabaseInterface, logger: LoggerInterface) -> None:
                self.database = database
                self.logger = logger
            
            def get_user(self, user_id: int) -> str:
                result = self.database.query(f"SELECT * FROM users WHERE id = {user_id}")
                self.logger.log(f"Retrieved user {user_id}")
                return result
        
        # Register all services
        container.register(DatabaseInterface, TestDatabase)
        container.register(LoggerInterface, TestLogger)
        container.register(UserService, UserService)
        
        # Use Depends to resolve the complex service
        user_service_depends = Depends(UserService)
        user_service = user_service_depends.resolve("user_service")
        
        # Verify the entire chain works
        assert isinstance(user_service, UserService)
        assert isinstance(user_service.database, TestDatabase)
        assert isinstance(user_service.logger, TestLogger)
        
        # Test functionality
        result = user_service.get_user(123)
        assert "SELECT * FROM users WHERE id = 123" in result
        assert "Retrieved user 123" in user_service.logger.logged_messages
