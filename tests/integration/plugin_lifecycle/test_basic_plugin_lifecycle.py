"""Test basic plugin lifecycle integration."""

from typing import Any

import pytest

from plugginger.api import Plugginger<PERSON>pp<PERSON><PERSON>er, PluginBase, plugin, service


@plugin(name="simple_service")
class SimpleServicePlugin(PluginBase):
    """Simple service plugin for testing."""

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.setup_called = False
        self.teardown_called = False
        self.data: dict[str, Any] = {}

    async def setup(self) -> None:
        """Setup the plugin."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Teardown the plugin."""
        self.teardown_called = True
        self.data.clear()

    @service()
    async def get_data(self) -> dict[str, Any]:
        """Get plugin data."""
        return self.data.copy()

    @service()
    async def set_value(self, key: str, value: Any) -> None:
        """Set a value."""
        self.data[key] = value

    @service()
    async def get_value(self, key: str) -> Any:
        """Get a value."""
        return self.data.get(key)


@plugin(name="counter_service")
class CounterServicePlugin(PluginBase):
    """Counter service plugin for testing."""

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.count = 0

    @service()
    async def increment(self) -> int:
        """Increment counter."""
        self.count += 1
        return self.count

    @service()
    async def decrement(self) -> int:
        """Decrement counter."""
        self.count -= 1
        return self.count

    @service()
    async def get_count(self) -> int:
        """Get current count."""
        return self.count

    @service()
    async def reset(self) -> None:
        """Reset counter."""
        self.count = 0


class TestBasicPluginLifecycle:
    """Test basic plugin lifecycle."""

    def test_plugin_registration(self) -> None:
        """Test plugin registration."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(SimpleServicePlugin)
            .register_plugin(CounterServicePlugin)
            .build()
        )

        # Check services are registered
        services = app.list_services()

        expected_services = [
            "simple_service.get_data",
            "simple_service.set_value",
            "simple_service.get_value",
            "counter_service.increment",
            "counter_service.decrement",
            "counter_service.get_count",
            "counter_service.reset",
        ]

        for service_name in expected_services:
            assert service_name in services

    @pytest.mark.asyncio
    async def test_basic_service_calls(self) -> None:
        """Test basic service calls."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(SimpleServicePlugin)
            .register_plugin(CounterServicePlugin)
            .build()
        )

        # Test simple service
        await app.call_service("simple_service.set_value", key="test", value="hello")
        result = await app.call_service("simple_service.get_value", key="test")
        assert result == "hello"

        # Test counter service
        count = await app.call_service("counter_service.increment")
        assert count == 1

        count = await app.call_service("counter_service.increment")
        assert count == 2

        current_count = await app.call_service("counter_service.get_count")
        assert current_count == 2

        await app.call_service("counter_service.reset")
        final_count = await app.call_service("counter_service.get_count")
        assert final_count == 0

    def test_multiple_apps_isolation(self) -> None:
        """Test that multiple apps are isolated."""
        app1 = PluggingerAppBuilder().register_plugin(CounterServicePlugin).build()

        app2 = PluggingerAppBuilder().register_plugin(CounterServicePlugin).build()

        # Apps should be different instances
        assert app1 is not app2

        # Services should be independent
        services1 = app1.list_services()
        services2 = app2.list_services()

        assert services1 == services2  # Same service names
        # But different plugin instances (can't easily test without internal access)

    def test_builder_validation(self) -> None:
        """Test builder validation."""
        builder = PluggingerAppBuilder()

        # Test building with plugins
        app = builder.register_plugin(SimpleServicePlugin).build()
        assert app.app_name == "PluggingerApp"  # Default name

        # Test custom app name
        app2 = (
            PluggingerAppBuilder()
            .with_name("CustomApp")
            .register_plugin(CounterServicePlugin)
            .build()
        )
        assert app2.app_name == "CustomApp"

    def test_plugin_metadata(self) -> None:
        """Test plugin metadata."""
        from plugginger.api.plugin import get_plugin_metadata

        # Test simple service metadata
        metadata = get_plugin_metadata(SimpleServicePlugin)
        assert metadata["name"] == "simple_service"

        # Test counter service metadata
        metadata = get_plugin_metadata(CounterServicePlugin)
        assert metadata["name"] == "counter_service"

    @pytest.mark.asyncio
    async def test_service_error_handling(self) -> None:
        """Test service error handling."""
        app = PluggingerAppBuilder().register_plugin(SimpleServicePlugin).build()

        # Test calling non-existent service
        with pytest.raises(Exception):
            await app.call_service("nonexistent.service")

        # Test calling service with wrong parameters
        with pytest.raises(Exception):
            await app.call_service("simple_service.set_value", wrong_param="test")

    def test_app_properties(self) -> None:
        """Test app instance properties."""
        app = (
            PluggingerAppBuilder().with_name("TestApp").register_plugin(SimpleServicePlugin).build()
        )

        assert app.app_name == "TestApp"
        assert hasattr(app, "call_service")
        assert hasattr(app, "emit_event")
        assert hasattr(app, "list_services")
        assert hasattr(app, "list_event_patterns")

    def test_builder_fluent_interface(self) -> None:
        """Test builder fluent interface."""
        app = (
            PluggingerAppBuilder()
            .with_name("FluentApp")
            .register_plugin(SimpleServicePlugin)
            .register_plugin(CounterServicePlugin)
            .build()
        )

        assert app.app_name == "FluentApp"

        services = app.list_services()
        assert len(services) >= 7  # At least 7 services from 2 plugins

    @pytest.mark.asyncio
    async def test_concurrent_service_calls(self) -> None:
        """Test concurrent service calls."""
        import asyncio

        app = PluggingerAppBuilder().register_plugin(CounterServicePlugin).build()

        # Make multiple concurrent increment calls
        tasks = [app.call_service("counter_service.increment") for _ in range(5)]
        results = await asyncio.gather(*tasks)

        # Results should be [1, 2, 3, 4, 5] (order may vary due to concurrency)
        assert sorted(results) == [1, 2, 3, 4, 5]

        # Final count should be 5
        final_count = await app.call_service("counter_service.get_count")
        assert final_count == 5

    def test_empty_app(self) -> None:
        """Test building app with no plugins."""
        app = PluggingerAppBuilder().build()

        assert app.app_name == "PluggingerApp"
        services = app.list_services()
        assert isinstance(services, list)
        # May have some built-in services

    @pytest.mark.asyncio
    async def test_plugin_data_persistence(self) -> None:
        """Test that plugin data persists across service calls."""
        app = PluggingerAppBuilder().register_plugin(SimpleServicePlugin).build()

        # Set multiple values
        await app.call_service("simple_service.set_value", key="name", value="Alice")
        await app.call_service("simple_service.set_value", key="age", value=30)
        await app.call_service("simple_service.set_value", key="city", value="Berlin")

        # Retrieve values
        name = await app.call_service("simple_service.get_value", key="name")
        age = await app.call_service("simple_service.get_value", key="age")
        city = await app.call_service("simple_service.get_value", key="city")

        assert name == "Alice"
        assert age == 30
        assert city == "Berlin"

        # Get all data
        all_data = await app.call_service("simple_service.get_data")
        expected_data = {"name": "Alice", "age": 30, "city": "Berlin"}
        assert all_data == expected_data
