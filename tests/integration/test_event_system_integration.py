# tests/integration/test_event_system_integration.py

"""
Integration tests for the Event system.

These tests verify that the event system components work together correctly
and provide comprehensive coverage of the event dispatcher functionality.
"""

from __future__ import annotations

import asyncio
import os
import sys
from typing import Any
from unittest.mock import Mock

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))


@pytest.mark.asyncio
async def test_complete_event_workflow() -> None:
    """Test a complete event workflow from registration to cleanup."""
    from plugginger.core.config import EventListenerFaultPolicy
    from plugginger.implementations.events import (
        Simple<PERSON>ventDispatcher,
        SimpleEventFaultHandler,
        SimpleEventRegistry,
    )

    # Setup components
    mock_logger = Mock()
    fault_handler = SimpleEventFaultHandler(
        EventListenerFaultPolicy.LOG_AND_CONTINUE, logger=mock_logger
    )
    dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)
    registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

    # Track events
    events_received: list[tuple[str, dict[str, Any], str | None]] = []

    async def user_event_handler(event_data: dict[str, Any], event_type: str | None = None) -> None:
        events_received.append(("user_handler", event_data, event_type))

    async def admin_event_handler(event_data: dict[str, Any]) -> None:
        events_received.append(("admin_handler", event_data, None))

    # Register listeners through registry
    registry.register_listener(
        "user_plugin", "on_user_events", ["user.created", "user.updated"], user_event_handler
    )
    registry.register_listener(
        "admin_plugin", "on_admin_events", ["admin.*"], admin_event_handler
    )

    # Emit various events
    await dispatcher.emit_event("user.created", {"user_id": 123, "name": "Alice"})
    await dispatcher.emit_event("user.updated", {"user_id": 123, "email": "<EMAIL>"})
    await dispatcher.emit_event("admin.login", {"admin_id": 456})
    await dispatcher.emit_event("system.startup", {"version": "1.0.0"})  # No listeners

    # Wait for async processing
    await asyncio.sleep(0.1)

    # Verify events were received correctly
    assert len(events_received) == 4  # 3 matching events + 1 wildcard match

    # Check specific events
    user_created = next(e for e in events_received if e[1].get("user_id") == 123 and e[1].get("name"))
    assert user_created[0] == "user_handler"
    assert user_created[2] == "user.created"

    user_updated = next(e for e in events_received if e[1].get("email"))
    assert user_updated[0] == "user_handler"
    assert user_updated[2] == "user.updated"

    admin_login = next(e for e in events_received if e[1].get("admin_id"))
    assert admin_login[0] == "admin_handler"

    # Test plugin cleanup
    user_listeners_before = registry.get_plugin_listeners("user_plugin")
    assert len(user_listeners_before) == 2

    removed_count = registry.unregister_plugin_listeners("user_plugin")
    assert removed_count == 2

    user_listeners_after = registry.get_plugin_listeners("user_plugin")
    assert len(user_listeners_after) == 0

    # Verify listeners are actually removed
    events_received.clear()
    await dispatcher.emit_event("user.created", {"user_id": 789})
    await asyncio.sleep(0.1)

    # Only admin handler should receive events now (no user.* pattern for admin)
    assert len(events_received) == 0

    # Cleanup
    await dispatcher.shutdown()


@pytest.mark.asyncio
async def test_fault_tolerance_integration() -> None:
    """Test fault tolerance across the entire event system."""
    from plugginger.core.config import EventListenerFaultPolicy
    from plugginger.implementations.events import (
        SimpleEventDispatcher,
        SimpleEventFaultHandler,
        SimpleEventRegistry,
    )

    # Setup with different fault policies
    mock_logger = Mock()
    isolate_handler = SimpleEventFaultHandler(
        EventListenerFaultPolicy.ISOLATE_AND_LOG, logger=mock_logger
    )
    dispatcher = SimpleEventDispatcher(isolate_handler, logger=mock_logger)
    registry = SimpleEventRegistry(dispatcher, logger=mock_logger)

    # Track successful calls
    successful_calls: list[str] = []

    async def reliable_listener(event_data: dict[str, Any]) -> None:
        successful_calls.append("reliable")

    async def failing_listener(event_data: dict[str, Any]) -> None:
        successful_calls.append("failing_called")
        raise ValueError("Simulated failure")

    # Register both listeners for the same event
    registry.register_listener(
        "reliable_plugin", "on_test", ["test.event"], reliable_listener
    )
    registry.register_listener(
        "failing_plugin", "on_test", ["test.event"], failing_listener
    )

    # First event - both should be called, one fails
    await dispatcher.emit_event("test.event", {"attempt": 1})
    await asyncio.sleep(0.1)

    assert "reliable" in successful_calls
    assert "failing_called" in successful_calls

    # Second event - failing listener should be isolated
    successful_calls.clear()
    await dispatcher.emit_event("test.event", {"attempt": 2})
    await asyncio.sleep(0.1)

    assert "reliable" in successful_calls
    assert "failing_called" not in successful_calls  # Should be isolated

    # Verify isolation was logged
    isolation_logged = any(
        "isolated due to error" in str(call) for call in mock_logger.call_args_list
    )
    assert isolation_logged

    await dispatcher.shutdown()


@pytest.mark.asyncio
async def test_concurrent_event_processing() -> None:
    """Test concurrent event processing and limits."""
    from plugginger.core.config import EventListenerFaultPolicy
    from plugginger.implementations.events import (
        SimpleEventDispatcher,
        SimpleEventFaultHandler,
    )

    mock_logger = Mock()
    fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
    dispatcher = SimpleEventDispatcher(
        fault_handler, logger=mock_logger, max_concurrent_events=3
    )

    processing_order: list[int] = []
    completion_order: list[int] = []

    async def slow_listener(event_data: dict[str, Any]) -> None:
        event_id = event_data["id"]
        processing_order.append(event_id)
        await asyncio.sleep(0.1)  # Simulate work
        completion_order.append(event_id)

    # Register listener
    dispatcher.add_listener("slow.event", slow_listener)

    # Emit multiple events rapidly
    for i in range(5):
        await dispatcher.emit_event("slow.event", {"id": i})

    # Wait for processing
    await asyncio.sleep(0.3)

    # Verify all events were processed
    assert len(processing_order) == 5
    assert len(completion_order) == 5

    # Verify concurrent limit warning was logged
    warning_logged = any(
        "Max concurrent events reached" in str(call) for call in mock_logger.call_args_list
    )
    assert warning_logged

    await dispatcher.shutdown()


@pytest.mark.asyncio
async def test_pattern_matching_comprehensive() -> None:
    """Test comprehensive pattern matching scenarios."""
    from plugginger.core.config import EventListenerFaultPolicy
    from plugginger.implementations.events import (
        SimpleEventDispatcher,
        SimpleEventFaultHandler,
    )

    mock_logger = Mock()
    fault_handler = SimpleEventFaultHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE)
    dispatcher = SimpleEventDispatcher(fault_handler, logger=mock_logger)

    matched_events: list[tuple[str, str]] = []

    async def exact_listener(event_data: dict[str, Any], event_type: str | None = None) -> None:
        matched_events.append(("exact", event_type or "unknown"))

    async def wildcard_listener(event_data: dict[str, Any], event_type: str | None = None) -> None:
        matched_events.append(("wildcard", event_type or "unknown"))

    async def prefix_listener(event_data: dict[str, Any], event_type: str | None = None) -> None:
        matched_events.append(("prefix", event_type or "unknown"))

    # Register different pattern types
    dispatcher.add_listener("user.created", exact_listener)  # Exact match
    dispatcher.add_listener("user.*", wildcard_listener)  # Wildcard
    dispatcher.add_listener("system.*", prefix_listener)  # Different prefix

    # Test various events
    test_events = [
        "user.created",  # Should match exact + wildcard
        "user.updated",  # Should match wildcard only
        "user.deleted",  # Should match wildcard only
        "system.startup",  # Should match prefix only
        "admin.login",  # Should match none
    ]

    for event in test_events:
        await dispatcher.emit_event(event, {"test": True})

    await asyncio.sleep(0.1)

    # Verify pattern matching
    exact_matches = [e for e in matched_events if e[0] == "exact"]
    wildcard_matches = [e for e in matched_events if e[0] == "wildcard"]
    prefix_matches = [e for e in matched_events if e[0] == "prefix"]

    assert len(exact_matches) == 1  # Only user.created
    assert exact_matches[0][1] == "user.created"

    assert len(wildcard_matches) == 3  # user.created, user.updated, user.deleted
    wildcard_events = {e[1] for e in wildcard_matches}
    assert wildcard_events == {"user.created", "user.updated", "user.deleted"}

    assert len(prefix_matches) == 1  # Only system.startup
    assert prefix_matches[0][1] == "system.startup"

    await dispatcher.shutdown()


if __name__ == "__main__":
    # Run tests directly
    import asyncio

    async def run_tests() -> None:
        print("🧪 Running event system integration tests...")

        try:
            await test_complete_event_workflow()
            print("✅ Complete workflow test passed")

            await test_fault_tolerance_integration()
            print("✅ Fault tolerance test passed")

            await test_concurrent_event_processing()
            print("✅ Concurrent processing test passed")

            await test_pattern_matching_comprehensive()
            print("✅ Pattern matching test passed")

            print("\n🎉 All integration tests passed!")

        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback

            traceback.print_exc()

    asyncio.run(run_tests())
